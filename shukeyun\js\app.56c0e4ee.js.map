{"version": 3, "file": "js/app.56c0e4ee.js", "mappings": "wltKAoVSA,MAAM,e,GAIJA,MAAM,sB,iBA0CPA,MAAM,kB,GAGTA,MAAM,iB,GACHA,MAAM,kB,GACPA,MAAM,c,uBAIRA,MAAM,kB,GACHA,MAAM,kB,GACPA,MAAM,e,GAEFA,MAAM,Y,uBAETC,EAAAA,EAAAA,IAAqB,KAAlBD,MAAM,SAAO,S,uSA9DxBC,EAAAA,EAAAA,IA4CM,OA5CDD,OAAK,SAAC,SAAQ,aAAyBE,EAAAA,e,EAC1CD,EAAAA,EAAAA,IA0CM,MA1CNE,EA0CM,EAxCJF,EAAAA,EAAAA,IAAwF,OAAnFD,MAAM,OAAOI,IAAAC,EAA4BC,IAAI,GAAIC,QAAK,eAAEC,EAAAA,uBAAuB,OAEpFP,EAAAA,EAAAA,IAoBM,MApBNQ,EAoBM,gBAnBJC,EAAAA,EAAAA,IAA8GC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAAjFT,EAAAA,KAAKU,SAAO,CAA3BC,EAAKC,M,WAAnBJ,EAAAA,EAAAA,IAA8G,QAAlEK,IAAKD,EAAQP,QAAK,GAAEC,EAAAA,uBAAuBM,K,QAAUD,GAAI,Q,OAErGZ,EAAAA,EAAAA,IAOM,OAPDD,MAAM,OAAQO,QAAK,eAAEC,EAAAA,Y,EACxBQ,EAAAA,EAAAA,IAEaC,EAAAA,GAAAA,CAFDC,KAAK,qBAAmB,C,kBAClC,IAAsC,WAAtCF,EAAAA,EAAAA,IAAsCG,EAAAA,CAAfnB,MAAM,QAAM,iBAApBE,EAAAA,a,OAEjBc,EAAAA,EAAAA,IAEaC,EAAAA,GAAAA,CAFDC,KAAK,qBAAmB,C,kBAClC,IAAuC,WAAvCF,EAAAA,EAAAA,IAAuCI,EAAAA,CAAfpB,MAAM,QAAM,kBAApBE,EAAAA,a,SAIpBD,EAAAA,EAAAA,IAOM,OAPDD,MAAM,OAAQO,QAAK,eAAEC,EAAAA,Y,EACxBQ,EAAAA,EAAAA,IAEaC,EAAAA,GAAAA,CAFDC,KAAK,kBAAgB,C,kBAC/B,IAAqC,WAArCF,EAAAA,EAAAA,IAAqCK,EAAAA,CAAfrB,MAAM,QAAM,iBAApBE,EAAAA,a,OAEhBc,EAAAA,EAAAA,IAEaC,EAAAA,GAAAA,CAFDC,KAAK,qBAAmB,C,kBAClC,IAAuC,WAAvCF,EAAAA,EAAAA,IAAuCM,EAAAA,CAAftB,MAAM,QAAM,kBAApBE,EAAAA,a,WAKtBc,EAAAA,EAAAA,IAecO,EAAAA,CAfDvB,MAAM,oBAAkB,CAExBwB,UAAQ,SACjB,IAUmB,EAVnBR,EAAAA,EAAAA,IAUmBS,EAAAA,KAAAA,C,kBARf,IAAoC,gBADtCf,EAAAA,EAAAA,IAEoEC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAD3CT,EAAAA,KAAKU,SAAO,CAA3BC,EAAKC,M,WADfY,EAAAA,EAAAA,IAEoEC,EAAAA,CAD5BZ,IAAKD,EAC1CP,QAAK,GAAEC,EAAAA,uBAAuBM,I,mBAAQ,IAAQ,mBAAND,GAAI,M,iCAC/CG,EAAAA,EAAAA,IAEmBW,EAAAA,CAFApB,QAAK,eAAEC,EAAAA,WAAWoB,QAAA,I,mBACnC,IAAwC,EAAxC3B,EAAAA,EAAAA,IAAwC,qBAAhCC,EAAAA,OAAM,wB,OAEhBc,EAAAA,EAAAA,IAEmBW,EAAAA,CAFApB,QAAK,eAAEC,EAAAA,Y,mBACxB,IAAsE,EAAtEP,EAAAA,EAAAA,IAAsE,qBAA9DC,EAAAA,OAAQA,EAAAA,OAAM,eAAkBA,EAAAA,OAAM,qB,oCAVpD,IAA6C,EAA7Cc,EAAAA,EAAAA,IAA6Ca,EAAAA,CAApC7B,MAAM,aAAW,C,kBAAC,IAAQ,EAARgB,EAAAA,EAAAA,IAAQc,M,oCAkBzCpB,EAAAA,EAAAA,IAGMC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAHsDT,EAAAA,KAAK6B,UAAQ,CAApCC,EAASC,M,WAA9CvB,EAAAA,EAAAA,IAGM,OAHDV,MAAM,kBAAiEe,IAAKkB,G,EAC/EhC,EAAAA,EAAAA,IAAsD,OAAtDiC,GAAsD,QAAvBF,EAASG,OAAK,kBAC7CzB,EAAAA,EAAAA,IAAkJC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAAjDqB,EAASI,aAAW,CAAjDC,EAAYC,M,WAAhFZ,EAAAA,EAAAA,IAAkJa,EAAAA,CAA3IvC,OAAK,QAAEqC,EAAYG,OAAQH,YAAaA,EAAyEtB,IAAKuB,G,qDAE/HrC,EAAAA,EAAAA,IAKM,MALNwC,EAKM,EAJJxC,EAAAA,EAAAA,IAAuD,OAAvDyC,GAAuD,QAAxBxC,EAAAA,KAAKU,QAAQ,IAAD,IAC3CX,EAAAA,EAAAA,IAEM,MAFN0C,EAEM,gBADJjC,EAAAA,EAAAA,IAAkGC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAAtDT,EAAAA,KAAK0C,WAAS,CAA7B/B,EAAKC,M,WAAlCJ,EAAAA,EAAAA,IAAkG,OAA5FN,IAAKS,EAAKgC,IAA6C9B,IAAKD,EAAQP,QAAK,GAAEC,EAAAA,MAAMK,EAAKiC,M,wBAGhG7C,EAAAA,EAAAA,IAWM,MAXN8C,EAWM,EAVJ9C,EAAAA,EAAAA,IAAuD,OAAvD+C,GAAuD,QAAxB9C,EAAAA,KAAKU,QAAQ,IAAD,IAC3CX,EAAAA,EAAAA,IAQM,MARNgD,EAQM,gBAPJvC,EAAAA,EAAAA,IAMMC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IANsBT,EAAAA,KAAKgD,YAAU,CAA9BrC,EAAKC,M,WAAlBJ,EAAAA,EAAAA,IAMM,OANwCK,IAAKD,GAAK,EACtDb,EAAAA,EAAAA,IAGM,MAHNkD,EAGM,EAFJlD,EAAAA,EAAAA,IAA8C,OAAxCG,IAAKS,EAAKgC,IAAMtC,QAAK,GAAEC,EAAAA,MAAMK,EAAKiC,M,UACxCM,KAEFnD,EAAAA,EAAAA,IAA0B,qBAAlBY,EAAKK,MAAI,Q,wBCrPlBlB,MAAM,Q,GACJA,MAAM,Q,GACLA,MAAM,c,GACJA,MAAM,c,GAETA,MAAM,oB,8DALbU,EAAAA,EAAAA,IAiBM,MAjBNP,EAiBM,EAhBJF,EAAAA,EAAAA,IAGM,MAHNQ,EAGM,EAFJR,EAAAA,EAAAA,IAAiD,KAAjDoD,GAAiD,QAAxBC,EAAAA,YAAYnB,OAAK,IAC1ClC,EAAAA,EAAAA,IAAuD,OAAvDiC,GAAuD,QAA5BoB,EAAAA,YAAYC,SAAO,MAEhDtD,EAAAA,EAAAA,IAWM,MAXNwC,EAWM,gBAVJ/B,EAAAA,EAAAA,IASMC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAT4F2C,EAAAA,YAAYE,UAAQ,CAA3CC,EAASC,M,WAApFhD,EAAAA,EAAAA,IASM,OATDV,OAAK,SAAC,YAAW,oBAA8ByD,EAASX,OAA4D/B,IAAK2C,G,EAC5HzD,EAAAA,EAAAA,IAAgC,OAA1BG,IAAKqD,EAASZ,IAAKvC,IAAI,I,WAC7BL,EAAAA,EAAAA,IAA4B,oBAArBwD,EAASvC,MAAI,GACiCuC,EAASX,K,iBAAG,WAAjEpC,EAAAA,EAAAA,IAKS,U,MALAV,OAAK,YAAMyD,EAASE,SAASC,S,gBACpClD,EAAAA,EAAAA,IAGMC,EAAAA,GAAAA,MAAAA,EAAAA,EAAAA,IAHgD8C,EAASE,UAAQ,CAAxCE,EAASC,M,WAAxCpD,EAAAA,EAAAA,IAGM,OAHDV,MAAM,YAA+De,IAAK+C,G,EAC7E7D,EAAAA,EAAAA,IAAgC,OAA1BG,IAAKyD,EAAShB,IAAKvC,IAAI,I,WAC7BL,EAAAA,EAAAA,IAA4B,oBAArB4D,EAAS3C,MAAI,Q,0CC1KhC,GACE6C,KAAM,OACNnD,QAAS,CAAC,OAAO,SAAS,OAAO,QACjCmB,SAAU,CACR,CACEI,MAAO,OACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,QACPoB,QAAS,2CACTC,SAAU,CAER,CACEtC,KAAM,UACN2B,IAAKmB,EAAQ,MACblB,IAAK,kCACLa,SAAU,IAGZ,CACEzC,KAAM,UACN2B,IAAKmB,EAAQ,MACblB,IAAK,oCACLa,SAAU,MAKhB,CACEnB,MAAO,KACPL,MAAO,QACPoB,QAAS,yCACTC,SAAU,CAER,CACEtC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,YACN2B,IAAKmB,EAAQ,MACblB,IAAK,mDAEP,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,gEAEP,CACE5B,KAAM,eACN2B,IAAKmB,EAAQ,MACblB,IAAK,kCAEP,CACE5B,KAAM,eACN2B,IAAKmB,EAAQ,MACblB,IAAK,iDAEP,CACE5B,KAAM,WACN2B,IAAKmB,EAAQ,MACblB,IAAK,8CAEP,CACE5B,KAAM,aACN2B,IAAKmB,EAAQ,MACblB,IAAK,wDAKX,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BACLa,SAAU,QAMpB,CACExB,MAAO,SACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,QACPoB,QAAS,2CACTC,SAAU,CAER,CACEtC,KAAM,WACN2B,IAAKmB,EAAQ,MACblB,IAAK,2DACLa,SAAU,IAGZ,CACEzC,KAAM,kBACN2B,IAAKmB,EAAQ,MACblB,IAAK,6BACLa,SAAU,IAGZ,CACEzC,KAAM,kBACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,iCAEP,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,KACblB,IAAK,2CAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,QACPoB,QAAS,oDACTC,SAAU,CAER,CACEtC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,uCAEP,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,sCAKX,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BACLa,SAAU,IAGZ,CACEzC,KAAM,QACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,QACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,QACPoB,QAAS,6CACTC,SAAU,CAER,CACEtC,KAAM,OACN2B,IAAKmB,EAAQ,KACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,IAEP,CACE5B,KAAM,WACN2B,IAAKmB,EAAQ,MACblB,IAAK,wCAEP,CACE5B,KAAM,KACN2B,IAAKmB,EAAQ,MACblB,IAAK,mDAKX,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,qBACLa,SAAU,IA6BZ,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,WASrBI,WAAY,CACV,CACEhC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,QACN2B,IAAKmB,EAAQ,MACblB,IAAK,yBAGTF,UAAW,CACT,CACEC,IAAKmB,EAAQ,KACblB,IAAK,qBAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,6BAEP,CACED,IAAKmB,EAAQ,KACblB,IAAK,+BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,kCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,+BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,KAGToB,OAAQ,CACNC,QAAS,WACTC,UAAW,OACXC,KAAM,OC3cV,GACEN,KAAM,UACN5B,MAAO,gBACPvB,QAAQ,CAAC,gBAAgB,mBAAmB,mBAAmB,2BAC/DmB,SAAU,CACR,CACEI,MAAO,gBACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,oBACPoB,QAAS,qLACTC,SAAU,CAER,CACEtC,KAAM,UACN2B,IAAKmB,EAAQ,MAEbL,SAAU,IAGZ,CACEzC,KAAM,4BACN2B,IAAKmB,EAAQ,MAEbL,SAAU,MAKhB,CACEnB,MAAO,KACPL,MAAO,uBACPoB,QAAS,qLACTC,SAAU,CAER,CACEtC,KAAM,uBACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,gCACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,iBACN2B,IAAKmB,EAAQ,MACblB,IAAK,gEAEP,CACE5B,KAAM,2BACN2B,IAAKmB,EAAQ,MACblB,IAAK,kCAEP,CACE5B,KAAM,oBACN2B,IAAKmB,EAAQ,MACblB,IAAK,iDAEP,CACE5B,KAAM,0BACN2B,IAAKmB,EAAQ,MACblB,IAAK,8CAEP,CACE5B,KAAM,+BACN2B,IAAKmB,EAAQ,MACblB,IAAK,wDAKX,CACE5B,KAAM,gBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BACLa,SAAU,QAMpB,CACExB,MAAO,mBACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,yBACPoB,QAAS,wMACTC,SAAU,CAER,CACEtC,KAAM,sCACN2B,IAAKmB,EAAQ,MACblB,IAAK,2DACLa,SAAU,IAGZ,CACEzC,KAAM,+DACN2B,IAAKmB,EAAQ,MACblB,IAAK,6BACLa,SAAU,IAGZ,CACEzC,KAAM,kDACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,eACN2B,IAAKmB,EAAQ,MACblB,IAAK,iCAEP,CACE5B,KAAM,UACN2B,IAAKmB,EAAQ,KACblB,IAAK,2CAEP,CACE5B,KAAM,gBACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,wBACPoB,QAAS,kMACTC,SAAU,CAER,CACEtC,KAAM,WACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,UACN2B,IAAKmB,EAAQ,MACblB,IAAK,uCAEP,CACE5B,KAAM,uBACN2B,IAAKmB,EAAQ,MACblB,IAAK,sCAKX,CACE5B,KAAM,2BACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BACLa,SAAU,IAGZ,CACEzC,KAAM,+BACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,qBACN2B,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACE5B,KAAM,sBACN2B,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACE5B,KAAM,kBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,iCACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,sBACPoB,QAAS,qMACTC,SAAU,CAER,CACEtC,KAAM,OACN2B,IAAKmB,EAAQ,KACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,aACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,IAEP,CACE5B,KAAM,gCACN2B,IAAKmB,EAAQ,MACblB,IAAK,wCAEP,CACE5B,KAAM,UACN2B,IAAKmB,EAAQ,MACblB,IAAK,mDAKX,CACE5B,KAAM,0BACN2B,IAAKmB,EAAQ,MACblB,IAAK,qBACLa,SAAU,IA6BZ,CACEzC,KAAM,oBACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,qBACN2B,IAAKmB,EAAQ,MACbC,KAAMD,EAAQ,MACdlB,IAAK,WASrBI,WAAY,CACV,CACEhC,KAAM,cACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,aACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,iBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,cACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,wBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,mBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,YACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,YACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,gBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,mBACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,0BACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,cACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,YACN2B,IAAKmB,EAAQ,MACblB,IAAK,0BAEP,CACE5B,KAAM,kCACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,0BACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,gCACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,wBACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,yBACN2B,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACE5B,KAAM,8BACN2B,IAAKmB,EAAQ,MACblB,IAAK,yBAGTF,UAAW,CACT,CACEC,IAAKmB,EAAQ,KACblB,IAAK,qBAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,4BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,6BAEP,CACED,IAAKmB,EAAQ,KACblB,IAAK,+BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,kCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,2BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,oCAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,+BAEP,CACED,IAAKmB,EAAQ,MACblB,IAAK,KAGToB,OAAQ,CACNC,QAAS,6CACTC,UAAW,sBACXC,KAAM,YCxcH,MAAMC,GAAYC,EAAAA,EAAAA,IAAY,YAAa,CAChDC,MAAO,IAASC,aAAaC,QAAQ,UAAU,IAAIC,GAAI,IAAIC,KHgL7D,OACEC,MAAO,CAAC,eACRC,IAAAA,GACE,MAAO,CACLC,KAAMT,IAEV,EACAU,QAAS,CACPC,KAAAA,CAAMpE,GACAA,EAAKiC,IACPoC,OAAOC,KAAKtE,EAAKiC,KACRjC,EAAKoD,KACdmB,KAAKC,QAAQ,CACXlD,MAAOtB,EAAKK,KACZoE,YAAa,eACbC,0BAA0B,EAC1BC,QAAU,aAAY3E,EAAKoD,SAC3BwB,SAAU,IAGZL,KAAKC,QAAQ,CACXlD,OAAOuD,EAAAA,EAAAA,IAAYpB,KAAaP,KAChC4B,KAAM,WAGZ,I,UItMJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,Q,6BCTOC,QAAQ,YAAY7F,MAAM,YAAY,kBAAgB,I,GAAGC,EAAAA,EAAAA,IAAmN,QAA7M6F,EAAE,gLAAgLC,KAAK,gB,YAA7LtF,G,kCAA9DC,EAAAA,EAAAA,IAAuR,MAAvRP,EAAuR,E,CCAzR,MAAM6F,EAAS,CAAC,EAGV,GAA2B,OAAgBA,EAAQ,CAAC,CAAC,SAAS,KAEpE,Q,SCLOH,QAAQ,YAAY7F,MAAM,aAAa,kBAAgB,I,GAAGC,EAAAA,EAAAA,IAAs+B,QAAh+B6F,EAAE,m8BAAm8BC,KAAK,gB,YAAh9BtF,G,kCAA/DC,EAAAA,EAAAA,IAA2iC,MAA3iCP,EAA2iC,E,CCA7iC,MAAM,EAAS,CAAC,EAGV,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,KAEpE,Q,UCJIF,EAAAA,EAAAA,IAAQ,SAAL,KAAC,G,IACJA,EAAAA,EAAAA,IAAY,WAAP,KAAC,G,IADNE,GACAM,I,mCAFFC,EAAAA,EAAAA,IAGM,c,CCHR,MAAM,GAAS,CAAC,EAGV,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,MAEpE,U,UCJIT,EAAAA,EAAAA,IAAQ,SAAL,KAAC,G,IACJA,EAAAA,EAAAA,IAAY,WAAP,KAAC,G,IADNE,GACAM,I,mCAFFC,EAAAA,EAAAA,IAGM,c,CCHR,MAAM,GAAS,CAAC,EAGV,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,MAEpE,UbsaA,IACEQ,KAAM,MACN+E,WAAY,CACVC,KAAI,EACJC,KAAI,MACJC,MAAK,GACLC,MAAK,GACLC,KAAI,EACJC,MAAKA,GAEPzB,IAAAA,GACE,MAAO,CACLC,MAAMW,EAAAA,EAAAA,IAAYpB,KAClBkC,QAAQ,EACRC,QAAQC,EAAAA,EAAAA,OACRC,QAAQ,EACRC,YAAY,EACZC,KAAM,GACN9E,SAAU,CACR,CACEI,MAAO,OACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,QACPoB,QAAS,2CACTC,SAAU,CAER,CACEtC,KAAM,aACN2B,IAAKmB,EAAQ,MAEbL,SAAU,IAGZ,CACEzC,KAAM,UACN2B,IAAKmB,EAAQ,MAEbL,SAAU,MAKhB,CACEnB,MAAO,KACPL,MAAO,QACPoB,QAAS,yCACTC,SAAU,CAER,CACEtC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,eACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,eACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,WACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,aACN2B,IAAKmB,EAAQ,SAMnB,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,YAS3B,CACE7B,MAAO,SACPC,YAAa,CAEX,CACEI,MAAO,KACPL,MAAO,QACPoB,QAAS,2CACTC,SAAU,CAER,CACEtC,KAAM,WACN2B,IAAKmB,EAAQ,MAEbL,SAAU,IAGZ,CACEzC,KAAM,kBACN2B,IAAKmB,EAAQ,MAEbL,SAAU,IAGZ,CACEzC,KAAM,kBACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,MAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,MACblB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,QACPoB,QAAS,oDACTC,SAAU,CAER,CACEtC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,SAMnB,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,MAEbL,SAAU,IAGZ,CACEzC,KAAM,QACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,OACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,QACN2B,IAAKmB,EAAQ,MACblB,IAAK,QAOf,CACEN,MAAO,KACPL,MAAO,QACPoB,QAAS,6CACTC,SAAU,CACR,CACEtC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CAER,CACEzC,KAAM,GACN2B,IAAKmB,EAAQ,OAIf,CACE9C,KAAM,GACN2B,IAAKmB,EAAQ,OAIf,CACE9C,KAAM,GACN2B,IAAKmB,EAAQ,OAIf,CACE9C,KAAM,GACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,MACN2B,IAAKmB,EAAQ,MACblB,IAAK,QAOf,CACE5B,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,GACLa,SAAU,CACR,CACEzC,KAAM,OACN2B,IAAKmB,EAAQ,MACblB,IAAK,WASrBI,WAAY,CACV,CACEhC,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,MACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,SACN2B,IAAKmB,EAAQ,OAGf,CACE9C,KAAM,QACN2B,IAAKmB,EAAQ,QAIjBpB,UAAW,CACT,CACEC,IAAKmB,EAAQ,MAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,IAEP,CACED,IAAKmB,EAAQ,OAGf,CACEnB,IAAKmB,EAAQ,MACblB,IAAK,KAIb,EACAgE,OAAAA,GAEE1B,KAAKqB,SAAWhC,aAAaC,QAAQ,UAErCU,KAAKoB,SAAW/B,aAAaC,QAAQ,UAErCU,KAAKuB,OAA4B,WAAnBI,SAASC,OAEvB9B,OAAO+B,iBAAiB,UAAU,KAChC7B,KAAKwB,WAAaxB,KAAK8B,SAAW,GAAE,GAExC,EACAC,OAAAA,GAEE/B,KAAKgC,aACLhC,KAAKyB,MAAO,IAAIQ,MAAOC,aACzB,EACAtC,QAAS,CAEPuC,OAAAA,GACEnC,KAAKqB,QAAUrB,KAAKqB,OACpBhC,aAAa+C,QAAQ,SAAUpC,KAAKqB,OAAO,SAAS,GACtD,EAEAgB,OAAAA,GACErC,KAAKoB,QAAUpB,KAAKoB,OACpBlC,IAAYoD,OAAStC,KAAKoB,OAAS,IAAImB,GAAU,IAAIC,GACrDnD,aAAa+C,QAAQ,SAAUpC,KAAKoB,OAAO,SAAS,GACtD,EAEAvB,KAAAA,CAAMnC,GAQJ,EAGF+E,MAAAA,CAAOC,EAAUC,GACf,OAAOC,SAASC,uBAAuBH,GAAWC,EACpD,EAEAb,MAAAA,GACE,OAAOhC,OAAOgD,aAAeF,SAASG,gBAAgBC,WAAaJ,SAASK,KAAKD,WAAa,CAChG,EAEAE,sBAAAA,CAAuBxH,GACrB,IAAIyH,EAAU,CACZnD,KAAKyC,OAAO,UAAU,GACtBzC,KAAKyC,OAAO,UAAU,GACtBzC,KAAKyC,OAAO,QAAQ,GACpBzC,KAAKyC,OAAO,SAAS,IACrB/G,GACFoE,OAAOsD,SAAS,CACZC,IAAKrD,KAAK8B,SAAWqB,EAAQG,wBAAwBD,IAAM,GAC3DE,SAAU,UAEhB,EAEAvB,UAAAA,GACE,IAAK,IAAIwB,KAAOZ,SAASC,uBAAuB,cAE9C,IAAIY,sBAAqBC,IACnBA,EAAQ,GAAGC,gBACbH,EAAII,aAAa,QAAQ,0CAC3B,IACCC,QAAQL,EAEf,Ich7BJ,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAASM,KAEpE,U,oCCDAC,EAAAA,EAAAA,IAAUC,IAAKC,KAAIC,EAAAA,EAAAA,OAAeD,IAAIE,GAAAA,GAAaC,MAAM,O,k5pECPrDC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUM,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,CAGAJ,EAAoBQ,EAAIF,E,WCzBxB,IAAIG,EAAW,GACfT,EAAoBU,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAASvG,OAAQ+G,IAAK,CACrCL,EAAWH,EAASQ,GAAG,GACvBJ,EAAKJ,EAASQ,GAAG,GACjBH,EAAWL,EAASQ,GAAG,GAE3B,IAJA,IAGIC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAAS1G,OAAQiH,MACpB,EAAXL,GAAsBC,GAAgBD,IAAaM,OAAOC,KAAKrB,EAAoBU,GAAGY,OAAM,SAASjK,GAAO,OAAO2I,EAAoBU,EAAErJ,GAAKuJ,EAASO,GAAK,IAChKP,EAASW,OAAOJ,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbT,EAASc,OAAON,IAAK,GACrB,IAAIO,EAAIX,SACEV,IAANqB,IAAiBb,EAASa,EAC/B,CACD,CACA,OAAOb,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAASvG,OAAQ+G,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAd,EAAoByB,EAAI,SAASpB,GAChC,IAAIqB,EAASrB,GAAUA,EAAOsB,WAC7B,WAAa,OAAOtB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAL,EAAoB5D,EAAEsF,EAAQ,CAAEE,EAAGF,IAC5BA,CACR,C,eCNA1B,EAAoB5D,EAAI,SAASgE,EAASyB,GACzC,IAAI,IAAIxK,KAAOwK,EACX7B,EAAoB8B,EAAED,EAAYxK,KAAS2I,EAAoB8B,EAAE1B,EAAS/I,IAC5E+J,OAAOW,eAAe3B,EAAS/I,EAAK,CAAE2K,YAAY,EAAMC,IAAKJ,EAAWxK,IAG3E,C,eCPA2I,EAAoBkC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOzG,MAAQ,IAAI0G,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,kBAAX7G,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBwE,EAAoB8B,EAAI,SAASQ,EAAKC,GAAQ,OAAOnB,OAAOoB,UAAUC,eAAelC,KAAK+B,EAAKC,EAAO,C,eCAtGvC,EAAoB0C,EAAI,E,eCKxB,IAAIC,EAAkB,CACrB,IAAK,GAaN3C,EAAoBU,EAAES,EAAI,SAASyB,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4B1H,GAC/D,IAKI6E,EAAU2C,EALVhC,EAAWxF,EAAK,GAChB2H,EAAc3H,EAAK,GACnB4H,EAAU5H,EAAK,GAGI6F,EAAI,EAC3B,GAAGL,EAASqC,MAAK,SAASC,GAAM,OAA+B,IAAxBP,EAAgBO,EAAW,IAAI,CACrE,IAAIjD,KAAY8C,EACZ/C,EAAoB8B,EAAEiB,EAAa9C,KACrCD,EAAoBQ,EAAEP,GAAY8C,EAAY9C,IAGhD,GAAG+C,EAAS,IAAIrC,EAASqC,EAAQhD,EAClC,CAEA,IADG8C,GAA4BA,EAA2B1H,GACrD6F,EAAIL,EAAS1G,OAAQ+G,IACzB2B,EAAUhC,EAASK,GAChBjB,EAAoB8B,EAAEa,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO5C,EAAoBU,EAAEC,EAC9B,EAEIwC,EAAqBC,KAAK,2BAA6BA,KAAK,4BAA8B,GAC9FD,EAAmBE,QAAQR,EAAqBS,KAAK,KAAM,IAC3DH,EAAmBI,KAAOV,EAAqBS,KAAK,KAAMH,EAAmBI,KAAKD,KAAKH,G,IC/CvF,IAAIK,EAAsBxD,EAAoBU,OAAEP,EAAW,CAAC,MAAM,WAAa,OAAOH,EAAoB,KAAO,IACjHwD,EAAsBxD,EAAoBU,EAAE8C,E", "sources": ["webpack://demo-vue3.0/./src/App.vue", "webpack://demo-vue3.0/./src/components/Wrap.vue", "webpack://demo-vue3.0/./src/store/zh.js", "webpack://demo-vue3.0/./src/store/en.js", "webpack://demo-vue3.0/./src/store/lang.js", "webpack://demo-vue3.0/./src/components/Wrap.vue?1149", "webpack://demo-vue3.0/./src/assets/svg/dark.vue", "webpack://demo-vue3.0/./src/assets/svg/dark.vue?5356", "webpack://demo-vue3.0/./src/assets/svg/light.vue", "webpack://demo-vue3.0/./src/assets/svg/light.vue?4619", "webpack://demo-vue3.0/./src/assets/svg/langZ.vue", "webpack://demo-vue3.0/./src/assets/svg/langZ.vue?28c2", "webpack://demo-vue3.0/./src/assets/svg/langE.vue", "webpack://demo-vue3.0/./src/assets/svg/langE.vue?3414", "webpack://demo-vue3.0/./src/App.vue?7ccd", "webpack://demo-vue3.0/./src/main.js", "webpack://demo-vue3.0/webpack/bootstrap", "webpack://demo-vue3.0/webpack/runtime/chunk loaded", "webpack://demo-vue3.0/webpack/runtime/compat get default export", "webpack://demo-vue3.0/webpack/runtime/define property getters", "webpack://demo-vue3.0/webpack/runtime/global", "webpack://demo-vue3.0/webpack/runtime/hasOwnProperty shorthand", "webpack://demo-vue3.0/webpack/runtime/publicPath", "webpack://demo-vue3.0/webpack/runtime/jsonp chunk loading", "webpack://demo-vue3.0/webpack/startup"], "sourcesContent": ["<style lang=\"less\">\n// 滚动条样式\n::-webkit-scrollbar {\n  background: #f1f1f1;\n  width: 10px;\n  height: 10px;\n}\n::-webkit-scrollbar-thumb {\n  border-radius: 5px;\n  background-color: #c1c1c1;\n  border: 2px solid transparent;\n  background-clip: content-box;\n}\n::-webkit-scrollbar-thumb:hover {\n  background-color: #a8a8a8;\n}\n// 全局公共样式\nbody {\n  margin: 0;\n  background: #F7F8FB;\n}\na {\n  text-decoration: none;\n}\n// 页面样式\n#app {\n  .header {\n    position: sticky;\n    top: 0;\n    z-index: 999;\n    height: 80px;\n    background: #fff;\n    user-select: none;\n    .header__box {\n      margin: auto;\n      max-width: 1400px;\n      height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n      padding: 0 30px;\n      .logo {\n        margin-right: auto;\n        height: 50px;\n      }\n      .nav {\n        display: flex;\n        align-items: center;\n        >span {\n          margin: 0 10px;\n          color: #666;\n          background-color: rgba(209,209,240,0.2);\n          padding: 5px 10px;\n          border-radius: 4px;\n          cursor: pointer;\n        }\n        >span:hover {\n          color: #0167D8;\n        }\n      }\n      .lang {\n        margin: 0 10px;\n        width: 26px;\n        height: 24px;\n        position: relative;\n        cursor: pointer;\n        .icon {\n          position: absolute;\n          top: 0;\n          left: 0;\n        }\n      }\n      .lang:hover {\n        color: #0167D8;\n      }\n      .nav_small {\n        font-size: 40px;\n        color: #0167D8;\n        cursor: pointer;\n      }\n    }\n  }\n  .header_down {\n    box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);\n  }\n  .content {\n    text-align: center;\n    padding: 2.81vw 0 8.33vw; // 54px 0 160px\n    .content__title {\n      padding: 26px;\n      font-size: 34px;\n      font-weight: bold;\n      display: inline-block;\n    }\n  }\n  .project {\n    overflow-x: hidden;\n    .s1 h1::before {\n      background: #419BFE;\n    }\n    .s2 h1::before {\n      background: #D83434;\n    }\n    .s3 h1::before {\n      background: #69B053;\n    }\n    .s4 h1::before {\n      background: #FF7C00;\n    }\n    .s5 h1::before {\n      background: #F66EE9;\n    }\n    object::after{\n      content: \"\";\n      position: absolute;\n      top: 0;\n      right: 0;\n      width: 16px;\n      height: 100%;\n      pointer-events: none;\n      background: linear-gradient(to left,#F7F8FB,rgba(247,248,251,0));\n    }\n  }\n  .project:nth-child(2n+1) {\n    background: #fff;\n    object::after{\n      background: linear-gradient(to left,#fff,rgba(255,255,255,0));\n    }\n  }\n  .tools {\n    .tools__box {\n      margin: 35px auto 0;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: center;\n      max-width: 1090px;\n      >img {\n        margin: 25px 20px;\n        width: 178px;\n        height: 94px;\n        object-fit: contain;\n        transition: .3s;\n        cursor: pointer;\n      }\n      >img:hover {\n        transform: scale(1.1);\n      }\n    }\n  }\n  .scenic {\n    background: url('@/assets/img/scenic_bg.png') no-repeat center;\n    background-size: cover;\n    .scenic__box {\n      margin: 40px auto 0;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: center;\n      max-width: 1185px;\n      >div {\n        margin: 20px 12px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        .img__box {\n          position: relative;\n          border-radius: 4px;\n          overflow: hidden;\n          >img {\n            width: 213px;\n            height: 128px;\n            object-fit: cover;\n            display: block;\n            // transition: .3s;\n            cursor: pointer;\n          }\n          // >img:hover {\n          //   transform: scale(1.1);\n          // }\n          .light {\n            cursor: pointer;\n            position: absolute;\n            left: -100%;\n            top: 0;\n            width: 180px;\n            height: 100%;\n            background-image: linear-gradient(0deg,rgba(255,255,255,0),rgba(255,255,255,0.5),rgba(255,255,255,0));\n            transform: skewx(-25deg);\n            \n          }\n        }\n        .img__box:hover .light {\n          left: 120%;\n          transition: .8s;\n        } \n        >span {\n          margin-top: 12px;\n          font-size: 16px;\n          color: rgba(0,0,0,0.8);\n        }\n      }\n    }\n  }\n  .footer {\n    width: 100%;\n    height: 70px;\n    line-height: 16px;\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: center;\n    font-size: 16px;\n    color: #ADADAD;\n    padding: 0 30px;\n    box-sizing: border-box;\n    >a {\n      color: #ADADAD;\n    }\n    >a:hover {\n      color: #1890ff;\n    }\n    >i:hover {\n      cursor: pointer;\n    }\n  }\n  @media (max-width: 1221px) {\n    .wrap {\n      .right {\n        justify-content: center;\n      }\n    }\n  }\n  @media (max-width: 627px) {\n    .wrap {\n      .right {\n        padding: 0 83px;\n      }\n    }\n  }\n}\n// 深色主题样式\n:root {\n  --dark-theme: #252526;\n  --dark-bg: #1e1e1e;\n  --dark-text: rgba(255, 255, 255, .87);\n}\nhtml.dark {\n  ::-webkit-scrollbar {\n    background: #424242;\n  }\n  ::-webkit-scrollbar-thumb {\n    background-color: #686868;\n  }\n  ::-webkit-scrollbar-thumb:hover {\n    background-color: #7b7b7b;\n  }\n  body {\n    margin: 0;\n    background: var(--dark-theme);\n  }\n  #app {\n    .header {\n      background: var(--dark-bg);\n      .header__box {\n        .nav {\n          >span {\n            color: #999;\n            background-color: var(--dark-theme);\n          }\n          >span:hover {\n            color: #0167D8;\n          }\n        }\n        .nav_small {\n          color: #0167D8;\n        }\n      }\n    }\n    .header_down {\n      box-shadow: #000 0 6px 6px -6px;\n    }\n    .content {\n      .content__title {\n        color: var(--dark-text);\n      }\n    }\n    .project {\n      h1,pre {\n        color: var(--dark-text);\n      }\n      .logo-wrap:hover,.icon-wrap:hover {\n        >pre {\n          color: #0167D8;\n        }\n      }\n      object::after{\n        background: linear-gradient(to left,var(--dark-theme),rgba(37,37,38,0));\n      }\n    }\n    .project:nth-child(2n+1) {\n      background: var(--dark-bg);\n      object::after{\n        background: linear-gradient(to left,var(--dark-bg),rgba(30,30,30,0));\n      }\n    }\n    .scenic {\n      position: relative;\n      background: none;\n      .scenic__box {\n        >div {\n          >span {\n            color: var(--dark-text);\n          }\n        }\n      }\n    }\n    .scenic::before {\n      content: '';\n      position: absolute;\n      z-index: -1;\n      inset: 0;\n      background: url('@/assets/img/scenic_bg.png') no-repeat center ;\n      background-size: cover;\n      filter: brightness(0.1) saturate(200);\n    }\n    .footer {\n      color: var(--dark-text);\n      >a {\n        color: var(--dark-text);\n      }\n      >a:hover {\n        color: #1890ff;\n      }\n    }\n  }\n}\n</style>\n\n<template>\n  <!-- 头部 -->\n  <div class=\"header\" :class=\"{'header_down': headerDown}\">\n    <div class=\"header__box\">\n      <!-- logo -->\n      <img class=\"logo\" src=\"@/assets/logo/数科云.png\" alt=\"\" @click=\"scrollToTargetAdjusted(0)\">\n      <!-- 导航 -->\n      <div class=\"nav hidden-xs-only\">\n        <span v-for=\"(item,index) in lang.navList\" :key=\"index\" @click=\"scrollToTargetAdjusted(index)\">{{item}}</span>\n        <!-- 语言 -->\n        <div class=\"lang\" @click=\"setLang()\">\n          <transition name=\"el-zoom-in-center\">\n            <langE v-show=\"isLang\" class=\"icon\" />\n          </transition>\n          <transition name=\"el-zoom-in-center\">\n            <langZ v-show=\"!isLang\" class=\"icon\" />\n          </transition>\n        </div>\n        <!-- 主题 -->\n        <div class=\"lang\" @click=\"setDark()\">\n          <transition name=\"el-zoom-in-top\">\n            <dark v-show=\"isDark\" class=\"icon\" />\n          </transition>\n          <transition name=\"el-zoom-in-bottom\">\n            <light v-show=\"!isDark\" class=\"icon\" /> \n          </transition>\n        </div>\n      </div>\n      <!-- 导航（移动端） -->\n      <el-dropdown class=\"hidden-sm-and-up\">\n        <el-icon class=\"nav_small\"><Menu /></el-icon>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item\n              v-for=\"(item,index) in lang.navList\" :key=\"index\"\n              @click=\"scrollToTargetAdjusted(index)\">{{item}}</el-dropdown-item>\n            <el-dropdown-item @click=\"setLang()\" divided>\n              <span>{{isLang?'简体中文':'English'}}</span>\n            </el-dropdown-item>\n            <el-dropdown-item @click=\"setDark()\">\n              <span>{{isDark?(isLang?'Light':'普通模式'):(isLang?'Dark':'暗黑模式')}}</span>\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n  </div>\n  <!-- 主体 -->\n  <div class=\"content project\" v-for=\"(wrapItem,wrapIndex) in lang.wrapList\" :key=\"wrapIndex\">\n    <span class=\"content__title\">{{wrapItem.title}}</span>\n    <wrap :class=\"projectItem.style\" :projectItem=\"projectItem\" v-for=\"(projectItem,projectIndex) in wrapItem.projectList\" :key=\"projectIndex\"></wrap>\n  </div>\n  <div class=\"content tools\">\n    <span class=\"content__title\">{{lang.navList[2]}}</span>\n    <div class=\"tools__box\">\n      <img :src=\"item.img\" v-for=\"(item,index) in lang.toolsList\" :key=\"index\" @click=\"toUrl(item.url)\">\n    </div>\n  </div>\n  <div class=\"content scenic\">\n    <span class=\"content__title\">{{lang.navList[3]}}</span>\n    <div class=\"scenic__box\">\n      <div v-for=\"(item,index) in lang.scenicList\" :key=\"index\">\n        <div class=\"img__box\">\n          <img :src=\"item.img\" @click=\"toUrl(item.url)\">\n          <i class=\"light\"></i>\n        </div>\n        <span>{{item.name}}</span>\n      </div>\n    </div>\n  </div>\n  <!-- 尾部 -->\n  <!-- <div class=\"footer\" v-if=\"ishead\">\n    <span>&copy;&nbsp;2001-{{ date }}</span>\n    <a target=\"_blank\" href=\"http://www.hqshuke.com\">{{lang.footer.company}}</a>\n    <span>{{lang.footer.copyright}}&nbsp;&nbsp;|&nbsp;&nbsp;</span>\n  </div> -->\n</template>\n\n<script>\nimport wrap from './components/Wrap.vue'\nimport { Menu } from \"@element-plus/icons-vue\";\nimport { useDark } from '@vueuse/core'\nimport dark from \"@/assets/svg/dark.vue\";\nimport light from \"@/assets/svg/light.vue\";\nimport langZ from \"@/assets/svg/langZ.vue\";\nimport langE from \"@/assets/svg/langE.vue\";\nimport { storeToRefs } from 'pinia';\nimport { langStore } from '@/store/lang'\nimport zhLang from \"@/store/zh\";\nimport enLang from \"@/store/en\";\n\nexport default {\n  name: 'App',\n  components: {\n    wrap,\n    Menu,\n    langZ,\n    langE,\n    dark,\n    light,\n  },\n  data() {\n    return {\n      lang: storeToRefs(langStore()),\n      isLang: false,\n      isDark: useDark(),\n      ishead: true,\n      headerDown: false,\n      date: '',\n      wrapList: [\n        {\n          title: '基础能力',\n          projectList: [\n            // 数据分析\n            {\n              style: 's1',\n              title: '数据与分析',\n              content: '提供数据库查询、大规模计算、数据报表等服务的综合智能平台，提高客户数据分析效率。',\n              logoList: [\n                // 灵狐\n                {\n                  name: 'Deepfox 灵狐',\n                  img: require('@/assets/logo/灵狐.png'),\n                  // url: 'https://datafox.shukeyun.com/#/',\n                  iconList: [],\n                },\n                // 分析引擎\n                {\n                  name: '大数据分析引擎',\n                  img: require('./assets/logo/分析引擎.png'),\n                  // url: 'https://redash-prod.shukeyun.com/',\n                  iconList: [],\n                },\n              ]\n            },\n            // 支付安全\n            {\n              style: 's2',\n              title: '支付与安全',\n              content: '结合区块链技术，保证交易结算的安全性，有效整合景区和涉旅企业的资金结算问题。',\n              logoList: [\n                // 智旅链\n                {\n                  name: '智旅链',\n                  img: require('@/assets/logo/智旅链.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: 'BDS 区块链\\n浏览器',\n                      img: require('./assets/icon/BDS 区块链浏览器.png'),\n                      // url: 'https://explorer.zhilvlian.com',\n                    },\n                    {\n                      name: 'BDS NFT\\n浏览器',\n                      img: require('./assets/icon/BDS NFT 浏览器.png'),\n                      // url: 'https://nft.zhilvlian.com/#/home',\n                    },\n                    {\n                      name: '数字藏品\\nH5',\n                      img: require('./assets/icon/数字藏品 H5.png'),\n                      // url: 'https://nft.zhilvlian.com/blockchain/h5/#/',\n                    },\n                    {\n                      name: '数字藏品\\n管理后台',\n                      img: require('./assets/icon/数字藏品管理后台.png'),\n                      // url: 'https://nft.zhilvlian.com/blockchain/management/#/',\n                    },\n                  ],\n                },\n                // 云联网络\n                {\n                  name: '云联网络',\n                  img: require('./assets/logo/云联网络.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: '个人中心',\n                      img: require('./assets/icon/个人中心.png'),\n                      // url: 'https://www.upaypal.cn/personal/#/register?_k=8u2g9w',\n                    },\n                    {\n                      name: '商户中心',\n                      img: require('./assets/icon/商户中心.png'),\n                      // url: 'https://www.upaypal.cn/merchants',\n                    }\n                  ],\n                },\n              ]\n            },\n          ]\n        },\n        {\n          title: '行业 + 云',\n          projectList: [\n            // 生态监管\n            {\n              style: 's3',\n              title: '生态与监管',\n              content: '融合卫星通信、大数据等技术，构建全方位、多层次的生态监测平台，提高应急处置效率。',\n              logoList: [\n                // 监管\n                {\n                  name: '智慧旅游监管平台',\n                  img: require('@/assets/logo/朝天旅游.png'),\n                  // url: 'http://chaotian.zhlyjg.cn',\n                  iconList: [],\n                },\n                // 监管\n                {\n                  name: '生态监测与灾害预警\\n管理平台',\n                  img: require('@/assets/logo/生态监测.png'),\n                  // url: 'http://yanjinghe.zhlyjg.cn',\n                  iconList: [],\n                },\n                // 森林防火\n                {\n                  name: '森林防火监测预警和\\n指挥系统',\n                  img: require('@/assets/logo/森林防火.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: '数据大屏',\n                      img: require('@/assets/icon/数据大屏.png'),\n                      // url: 'https://prod.shukeyun.com/fire/web/',\n                    },\n                    {\n                      name: '管理后台',\n                      img: require('@/assets/icon/管理后台.png'),\n                      // url: 'https://test.shukeyun.com/fire/management/#/',\n                    },\n                    {\n                      name: 'APP 下载',\n                      img: require('@/assets/qrCode/森林防火.png'),\n                      url: '',\n                    },\n                  ],\n                },\n              ]\n            },\n            // 城市管理\n            {\n              style: 's4',\n              title: '城市与管理',\n              content: '基于全域数字底座，利用数实融合引擎，提供更方便快捷的管理，给用户带来更加舒适的“数字化”生活体验。',\n              logoList: [\n                // 车无忌\n                {\n                  name: '车无忌',\n                  img: require('./assets/logo/车无忌.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: '管理后台',\n                      img: require('./assets/icon/管理后台_.png'),\n                      // url: 'http://www.parkingfree.cn/o2o-admin',\n                    },\n                    {\n                      name: '物业后台',\n                      img: require('./assets/icon/物业后台.png'),\n                      // url: 'http://www.parkingfree.cn/o2o-cp',\n                    }\n                  ],\n                },\n                // 智慧社区\n                {\n                  name: '智慧社区系统',\n                  img: require('@/assets/logo/印象石灰.png'),\n                  // url: 'https://www.shgzsq.com/',\n                  iconList: [],\n                },\n                // 母婴室\n                {\n                  name: '智慧母婴室',\n                  img: require('@/assets/logo/智慧母婴室.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: '运营中心',\n                      img: require('@/assets/icon/运营中心.png'),\n                      // url: 'http://mng.quanzilx.com',\n                    },\n                    {\n                      name: '监管中心',\n                      img: require('@/assets/icon/监管中心.png'),\n                      // url: 'http://jgf.quanzilx.com',\n                    },\n                    {\n                      name: '管理中心',\n                      img: require('@/assets/icon/管理中心.png'),\n                      // url: 'http://my.quanzilx.com',\n                    },\n                    {\n                      name: '母婴室地图',\n                      img: require('@/assets/qrCode/母婴室.png'),\n                      url: '',\n                    }\n                  ],\n                },\n              ]\n            },\n            // 文化旅游\n            {\n              style: 's5',\n              title: '文化与旅游',\n              content: '深入旅游行业，提供旅游目的地数字化建设与运营的解决方案，打造全场景目的地云服务平台。',\n              logoList: [\n                {\n                  name: '智慧文旅',\n                  img: require('@/assets/logo/智慧文旅.png'),\n                  url: '',\n                  iconList: [\n                    // 慧旅云\n                    {\n                      name: '',\n                      img: require('@/assets/logo/慧旅云.png'),\n                      // url: 'https://prod.shukeyun.com/cas/login/#/login?appId=xZZLVvBpD79TQVxPWOnd',\n                    },\n                    // 慧景云\n                    {\n                      name: '',\n                      img: require('@/assets/logo/慧景云.png'),\n                      // url: 'https://huijingyun.net/#/dtbyc/welcome',\n                    },\n                    // 易旅通\n                    {\n                      name: '',\n                      img: require('@/assets/logo/易旅通.png'),\n                      // url: 'https://prod.shukeyun.com/cas/login/#/login?appId=yc3fvV45yEWablblgUoq',\n                    },\n                    // 易旅宝\n                    {\n                      name: '',\n                      img: require('@/assets/logo/易旅宝.png'),\n                      url: '',\n                      iconList: [\n                        {\n                          name: '易旅宝',\n                          img: require('@/assets/qrCode/易旅宝.png'),\n                          url: '',\n                        }\n                      ],\n                    },\n                  ]\n                },\n                // 检票平台\n                {\n                  name: '检票平台',\n                  img: require('@/assets/logo/检票平台.png'),\n                  url: '',\n                  iconList: [\n                    {\n                      name: '检票平台',\n                      img: require('@/assets/qrCode/检票.png'),\n                      url: '',\n                    }\n                  ],\n                },\n              ]\n            }\n          ]\n        },\n      ],\n      scenicList: [\n        {\n          name: '龙门阁',\n          img: require('@/assets/scenic/jq_01.png'),\n          // url: 'http://lmg.yeahtour.cn'\n        },\n        {\n          name: '水磨沟',\n          img: require('@/assets/scenic/jq_02.png'),\n          // url: 'http://smg.yeahtour.cn'\n        },\n        {\n          name: '明月峡',\n          img: require('@/assets/scenic/jq_03.png'),\n          // url: 'http://myx.yeahtour.cn'\n        },\n        {\n          name: '曾家山',\n          img: require('@/assets/scenic/jq_04.png'),\n          // url: 'http://zjs.yeahtour.cn'\n        },\n        {\n          name: '皇泽寺',\n          img: require('@/assets/scenic/jq_05.png'),\n          // url: 'http://hzs.yeahtour.cn'\n        },\n        {\n          name: '东河口',\n          img: require('@/assets/scenic/jq_06.png'),\n          // url: 'http://dhk.yeahtour.cn'\n        },\n        {\n          name: '唐家河',\n          img: require('@/assets/scenic/jq_07.png'),\n          // url: 'http://tjh.yeahtour.cn'\n        },\n        {\n          name: '梨博园',\n          img: require('@/assets/scenic/jq_08.png'),\n          // url: 'http://lby.yeahtour.cn'\n        },\n        {\n          name: '剑门关',\n          img: require('@/assets/scenic/jq_09.png'),\n          // url: 'http://jmg.yeahtour.cn'\n        },\n        {\n          name: '千佛崖',\n          img: require('@/assets/scenic/jq_10.png'),\n          // url: 'http://qfy.yeahtour.cn'\n        },\n        {\n          name: '卡尔城',\n          img: require('@/assets/scenic/jq_11.png'),\n          // url: 'http://kec.yeahtour.cn'\n        },\n        {\n          name: '米仓山大峡谷',\n          img: require('@/assets/scenic/jq_12.png'),\n          // url: 'http://dxg.yeahtour.cn'\n        },\n        {\n          name: '鼓城山',\n          img: require('@/assets/scenic/jq_13.png'),\n          // url: 'http://mcs.yeahtour.cn'\n        },\n        {\n          name: '旺苍县文化馆',\n          img: require('@/assets/scenic/jq_14.png'),\n          // url: 'http://wcwhg.yeahtour.cn'\n        },\n        {\n          name: '旺苍县图书馆',\n          img: require('@/assets/scenic/jq_15.png'),\n          // url: 'http://wctsg.yeahtour.cn'\n        },\n        {\n          name: '苍溪县文化馆',\n          img: require('@/assets/scenic/jq_16.png'),\n          // url: 'http://cxwhg.yeahtour.cn'\n        },\n        {\n          name: '苍溪县图书馆',\n          img: require('@/assets/scenic/jq_17.png'),\n          // url: 'http://cxtsg.yeahtour.cn'\n        },\n        {\n          name: '剑阁县图书馆',\n          img: require('@/assets/scenic/jq_18.png'),\n          // url: 'http://jgtsg.yeahtour.cn'\n        },\n        {\n          name: '荆州园博园',\n          img: require('@/assets/scenic/jq_19.png'),\n          // url: 'http://www.jzyby.com'\n        }\n      ],\n      toolsList: [\n        {\n          img: require('@/assets/tools/01.png'),\n          // url: 'http://10.8.13.68'\n        },\n        {\n          img: require('@/assets/tools/02.png'),\n          // url: 'https://10.8.1.221:31199'\n        },\n        {\n          img: require('@/assets/tools/03.png'),\n          // url: 'http://git.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/04.png'),\n          // url: 'http://prod-grafana.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/05.png'),\n          // url: 'http://grafana-prod.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/06.png'),\n          url: ''\n        },\n        {\n          img: require('@/assets/tools/07.png'),\n          // url: 'http://10.8.13.13/zabbix/'\n        },\n        {\n          img: require('@/assets/tools/08.png'),\n          // url: 'http://rancher.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/09.png'),\n          // url: 'https://10.8.11.60:8443'\n        },\n        {\n          img: require('@/assets/tools/10.png'),\n          // url: 'http://minio-prod.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/11.png'),\n          url: ''\n        },\n        {\n          img: require('@/assets/tools/12.png'),\n          // url: 'http://reg.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/13.png'),\n          url: ''\n        },\n        {\n          img: require('@/assets/tools/14.png'),\n          // url: 'http://10.8.11.55:8080/'\n        },\n        {\n          img: require('@/assets/tools/15.png'),\n          url: ''\n        },\n        {\n          img: require('@/assets/tools/16.png'),\n          // url: 'http://grafana-prod.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/17.png'),\n          // url: 'https://redash-prod.shukeyun.com'\n        },\n        {\n          img: require('@/assets/tools/18.png'),\n          url: ''\n        },\n        {\n          img: require('@/assets/tools/19.png'),\n          // url: 'http://**************:8069/'\n        },\n        {\n          img: require('@/assets/tools/20.png'),\n          url: ''\n        },\n      ]\n    }\n  },\n  created() {\n    // 默认主题\n    this.isDark = !!localStorage.getItem('isDark')\n    // 默认语言\n    this.isLang = !!localStorage.getItem('isLang')\n    // 屏蔽头部尾部\n    this.ishead = location.search != '?nohead'\n    // 滚动监听（头部阴影）\n    window.addEventListener('scroll', () => {\n      this.headerDown = this.getTop() > 100\n    })\n  },\n  mounted() {\n    // 布局动画\n    this.transition()\n    this.date = new Date().getFullYear()\n  },\n  methods: {\n    // 切换主题\n    setDark() {\n      this.isDark = !this.isDark\n      localStorage.setItem('isDark', this.isDark?'isDark':'')\n    },\n    // 切换语言\n    setLang() {\n      this.isLang = !this.isLang\n      langStore().$state = this.isLang ? {...enLang} : {...zhLang}\n      localStorage.setItem('isLang', this.isLang?'isLang':'')\n    },\n    // 跳转链接\n    toUrl(url) {\n      // if (url) {\n      //   window.open(url)\n      // } else {\n      //   this.$notify({\n      //     title: storeToRefs(langStore()).link,\n      //     type: 'warning'\n      //   })\n      // }\n    }, \n    // 获取 Dom\n    getEle(className,classIndex) {\n      return document.getElementsByClassName(className)[classIndex]\n    },\n    // 获取页面滚动高度\n    getTop() {\n      return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0\n    },\n    // 定位导航\n    scrollToTargetAdjusted(index) {\n      var element = [\n        this.getEle('content',0),\n        this.getEle('content',1),\n        this.getEle('tools',0),\n        this.getEle('scenic',0),\n      ][index]\n      window.scrollTo({\n          top: this.getTop() + element.getBoundingClientRect().top - 80,\n          behavior: \"smooth\"\n      });\n    },\n    // 过渡动画\n    transition() {\n      for (let dom of document.getElementsByClassName('transition')) {\n        // 监听元素显示隐藏\n        new IntersectionObserver(entries => {\n          if (entries[0].isIntersecting) {\n            dom.setAttribute('style','transform: translateX(0px); opacity: 1;')\n          }\n        }).observe(dom)\n      }\n    }\n  }\n}\n</script>\n", "<style lang=\"less\" scoped>\n  .wrap {\n    margin: auto;\n    margin-top: 1.98vw; // 38px\n    max-width: 1300px;\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-evenly;\n    align-items: center;\n    .left {\n      text-align: left;\n      margin: 30px;\n      max-width: 534px; // 534px / 27.8125vw\n      >h1 {\n        font-size: 26px;\n        font-weight: bold;\n        color: #222;\n        display: flex;\n        align-items: center;\n        line-height: 26px;\n        transform: translateX(200px);\n        opacity: 0;\n        transition: .8s;\n      }\n      >h1::before {\n        content: '';\n        width: 5px;\n        height: 26px;\n        display: inline-block;\n        margin-right: 10px;\n      }\n      >span {\n        font-size:  16px;\n        color: #999;\n        line-height: 30px;\n        display: block;\n        transform: translateX(200px);\n        opacity: 0;\n        transition: 1s;\n      }\n    }\n    .right {\n      width: 640px;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: flex-end;\n      transform: translateX(-200px);\n      opacity: 0;\n      transition: .8s;\n      .logo-wrap {\n        min-width: 154px;\n        min-height: 204px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        cursor: pointer;\n        position: relative;\n        >img {\n          width: 82px;\n          height: 82px;\n          object-fit: contain;\n          transition: .3s;\n          overflow: hidden;\n        }\n        pre {\n          margin: 0;\n          font-family: none;\n          font-size: 14px;\n          color: #333;\n          position: absolute;\n          top: 80%;\n          left: 50%;\n          transform: translateX(-50%);\n          text-align: center;\n          overflow: hidden;\n        }\n        >object {\n          width: 0;\n          opacity: 0;\n          display: flex;\n          transition: .8s;\n          overflow: hidden;\n          position: relative;\n          .icon-wrap {\n            width: 80px;\n            height: 204px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            position: relative;\n            >img {\n              width: 64px;\n              height: 44px;\n              object-fit: contain;\n              transition: .3s;\n            }\n            >pre {\n              top: 65%;\n            }\n          }\n          .icon-wrap:hover {\n            >img {\n              transform: scale(1.2);\n            }\n            >pre {\n              color: #0167D8;\n            }\n          }\n        }\n      }\n      .logo-wrap:hover {\n        >img {\n          transform: scale(1.1);\n        }\n        >pre {\n          color: #0167D8;\n        }\n      }\n      .logo-wrap-hover:hover {\n        >img,>pre {\n          width: 0;\n          opacity: 0;\n        }\n        >object {\n          opacity: 1;\n        }\n        .w1 {\n          width: 80px;\n        }\n        .w2 {\n          width: 160px;\n        }\n        .w3 {\n          width: 240px;\n        }\n        .w4 {\n          width: 320px;\n        }\n        .w5 {\n          width: 400px;\n        }\n        .w6 {\n          width: 480px;\n        }\n      }\n    }\n  }\n</style>\n<style lang=\"less\">\n  .customImgBox {\n    width: auto !important;\n    img {\n      width: 150px;\n    }\n  }\n</style>\n\n<template>\n  <div class=\"wrap\">\n    <div class=\"left\">\n      <h1 class=\"transition\">{{projectItem.title}}</h1>\n      <span class=\"transition\">{{projectItem.content}}</span>\n    </div>\n    <div class=\"right transition\">\n      <div class=\"logo-wrap\" :class=\"{'logo-wrap-hover': !logoItem.url}\" v-for=\"(logoItem,logoIndex) in projectItem.logoList\" :key=\"logoIndex\">\n        <img :src=\"logoItem.img\" alt=\"\">\n        <pre>{{logoItem.name}}</pre>\n        <object :class=\"'w'+logoItem.iconList.length\" v-if=\"!logoItem.url\">\n          <div class=\"icon-wrap\" v-for=\"(iconItem,iconIndex) in logoItem.iconList\" :key=\"iconIndex\">\n            <img :src=\"iconItem.img\" alt=\"\">\n            <pre>{{iconItem.name}}</pre>\n          </div>\n        </object>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { storeToRefs } from 'pinia';\nimport { langStore } from '@/store/lang'\nexport default {\n  props: ['projectItem'],\n  data() {\n    return {\n      lang: langStore(),\n    }\n  },\n  methods: {\n    toUrl(item) {\n      if (item.url) {\n        window.open(item.url)\n      } else if (item.code) {\n        this.$notify({\n          title: item.name,\n          customClass: 'customImgBox', \n          dangerouslyUseHTMLString: true,\n          message: `<img src=\"${item.code}\">`,\n          duration: 0,\n        })\n      } else {\n        this.$notify({\n          title: storeToRefs(langStore()).link,\n          type: 'warning'\n        })\n      }\n    }\n  }\n}\n</script>\n", "export default {\n  link: '暫無鏈接',\n  navList: ['基礎能力','行業 + 雲','運維工具','合作景區'],\n  wrapList: [\n    {\n      title: '基礎能力',\n      projectList: [\n        // 數據分析\n        {\n          style: 's1',\n          title: '數據與分析',\n          content: '提供數據庫查詢、大規模計算、數據報表等服務的綜合智能平臺，提高客戶數據分析效率。',\n          logoList: [\n            // 靈狐\n            {\n              name: 'DataFox',\n              img: require('@/assets/logo/灵狐.png'),\n              url: 'https://datafox.shukeyun.com/#/',\n              iconList: [],\n            },\n            // 分析引擎\n            {\n              name: '大數據分析引擎',\n              img: require('@/assets/logo/分析引擎.png'),\n              url: 'https://redash-prod.shukeyun.com/',\n              iconList: [],\n            },\n          ]\n        },\n        // 支付安全\n        {\n          style: 's2',\n          title: '支付與安全',\n          content: '結合區塊鏈技術，保證交易結算的安全性，有效整合景區和涉旅企業的資金結算問題。',\n          logoList: [\n            // 智旅鏈\n            {\n              name: '智旅鏈',\n              img: require('@/assets/logo/智旅链.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '區塊鏈\\n服務平臺',\n                  img: require('@/assets/icon/区块链服务平台.png'),\n                  url: 'https://prod.shukeyun.com/blockchain/homepage/#',\n                },\n                {\n                  name: '數據存證',\n                  img: require('@/assets/icon/数据存证.png'),\n                  url: 'https://prod.shukeyun.com/blockchain/evidence-ui/#/HomeIndex',\n                },\n                {\n                  name: 'BDS 區塊鏈\\n瀏覽器',\n                  img: require('@/assets/icon/BDS 区块链浏览器.png'),\n                  url: 'https://explorer.zhilvlian.com',\n                },\n                {\n                  name: 'BDS NFT\\n瀏覽器',\n                  img: require('@/assets/icon/BDS NFT 浏览器.png'),\n                  url: 'https://nft.zhilvlian.com/#/DigitalCollection',\n                },\n                {\n                  name: '數字藏品\\nH5',\n                  img: require('@/assets/icon/数字藏品 H5.png'),\n                  url: 'https://nft.zhilvlian.com/blockchain/h5/#/',\n                },\n                {\n                  name: '數字藏品\\n管理後臺',\n                  img: require('@/assets/icon/数字藏品管理后台.png'),\n                  url: 'https://nft.zhilvlian.com/blockchain/management/#/',\n                },\n              ],\n            },\n            // 云联网络\n            {\n              name: '雲聯網絡',\n              img: require('@/assets/logo/云联网络.png'),\n              url: 'https://www.upaypal.com',\n              iconList: [],\n            },\n          ]\n        },\n      ]\n    },\n    {\n      title: '行業 + 雲',\n      projectList: [\n        // 生態監管\n        {\n          style: 's3',\n          title: '生態與監管',\n          content: '融合衛星通信、大數據等技術，構建全方位、多層次的生態監測平臺，提高應急處置效率。',\n          logoList: [\n            // 監管\n            {\n              name: '智慧旅遊監管平臺',\n              img: require('@/assets/logo/朝天旅游.png'),\n              url: 'https://prod.shukeyun.com/data/dashboard/#/?id=zly_01_01',\n              iconList: [],\n            },\n            // 监管\n            {\n              name: '生態監測與災害預警\\n管理平臺',\n              img: require('@/assets/logo/生态监测.png'),\n              url: 'http://yanjinghe.zhlyjg.cn',\n              iconList: [],\n            },\n            // 森林防火\n            {\n              name: '森林防火監測預警和\\n指揮系統',\n              img: require('@/assets/logo/森林防火.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '數據大屏',\n                  img: require('@/assets/icon/数据大屏.png'),\n                  url: 'https://slfh.shengtaiyun.com/',\n                },\n                {\n                  name: '管理後臺',\n                  img: require('@/assets/icon/管理后台.png'),\n                  url: 'https://slfh.shengtaiyun.com/management',\n                },\n                {\n                  name: 'APP 下載',\n                  img: require('@/assets/qrCode/森林防火.png'),\n                  code: require('@/assets/qrCode/森林防火.png'),\n                  url: '',\n                },\n              ],\n            },\n          ]\n        },\n        // 城市管理\n        {\n          style: 's4',\n          title: '城市與管理',\n          content: '基於全域數字底座，利用數實融合引擎，提供更方便快捷的管理，給用戶帶來更加舒適的“數字化”生活體驗。',\n          logoList: [\n            // 車無忌\n            {\n              name: '車無忌',\n              img: require('@/assets/logo/车无忌.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '管理後臺',\n                  img: require('@/assets/icon/管理后台_.png'),\n                  url: 'http://www.parkingfree.cn/o2o-admin',\n                },\n                {\n                  name: '物業後臺',\n                  img: require('@/assets/icon/物业后台.png'),\n                  url: 'http://www.parkingfree.cn/o2o-cp',\n                }\n              ],\n            },\n            // 智慧社区\n            {\n              name: '智慧社區系統',\n              img: require('@/assets/logo/印象石灰.png'),\n              url: 'https://www.shgzsq.com/',\n              iconList: [],\n            },\n            // 母婴室\n            {\n              name: '智慧母嬰室',\n              img: require('@/assets/logo/智慧母婴室.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '運營中心',\n                  img: require('@/assets/icon/运营中心.png'),\n                  url: 'http://mng.quanzilx.com',\n                },\n                {\n                  name: '監管中心',\n                  img: require('@/assets/icon/监管中心.png'),\n                  url: 'http://jgf.quanzilx.com',\n                },\n                {\n                  name: '管理中心',\n                  img: require('@/assets/icon/管理中心.png'),\n                  url: 'http://my.quanzilx.com',\n                },\n                {\n                  name: '母嬰室地圖',\n                  img: require('@/assets/qrCode/母婴室.png'),\n                  code: require('@/assets/qrCode/母婴室.png'),\n                  url: '',\n                }\n              ],\n            },\n          ]\n        },\n        // 文化旅游\n        {\n          style: 's5',\n          title: '文化與旅遊',\n          content: '深入旅遊行業，提供旅遊目的地數字化建設與運營的解決方案，打造全場景目的地雲服務平臺。',\n          logoList: [\n            // 途途\n            {\n              name: 'TUTU',\n              img: require('@/assets/logo/途途.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '途途伴遊',\n                  img: require('@/assets/qrCode/途途.png'),\n                  code: require('@/assets/qrCode/途途.png'),\n                  url: '',\n                },\n                {\n                  name: '數智人 TUTU',\n                  img: require('@/assets/icon/tutu.png'),\n                  url: 'https://prod.shukeyun.com/meta/chat/',\n                },\n                {\n                  name: '後臺',\n                  img: require('@/assets/icon/物业后台.png'),\n                  url: 'https://meta.zhilvlian.com/management/welcome',\n                }\n              ],\n            },\n            // 智慧文旅\n            {\n              name: '智慧文旅',\n              img: require('@/assets/logo/智慧文旅.png'),\n              url: 'https://hly.net/#/',\n              iconList: [\n                // // 慧旅云\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/慧旅云.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=xZZLVvBpD79TQVxPWOnd',\n                // },\n                // // 慧景云\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/慧景云.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=QMMS89YvTZ0Qs5sRWXFL&tk=null&scenicCode=yndl',\n                // },\n                // // 易旅通\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/易旅通.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=yc3fvV45yEWablblgUoq',\n                // },\n                // // 易旅宝\n                // {\n                //   name: '易旅宝',\n                //   img: require('@/assets/qrCode/易旅宝.png'),\n                //   code: require('@/assets/qrCode/易旅宝.png'),\n                //   url: '',\n                // },\n              ]\n            },\n            // 检票平台\n            {\n              name: '檢票平臺',\n              img: require('@/assets/logo/检票平台.png'),\n              url: '',\n              iconList: [\n                {\n                  name: '檢票平臺',\n                  img: require('@/assets/qrCode/检票.png'),\n                  code: require('@/assets/qrCode/检票.png'),\n                  url: '',\n                }\n              ],\n            },\n          ]\n        }\n      ]\n    },\n  ],\n  scenicList: [\n    {\n      name: '曾家山',\n      img: require('@/assets/scenic/jq_04.png'),\n      url: 'http://zjs.yeahtour.cn'\n    },\n    {\n      name: '明月峽',\n      img: require('@/assets/scenic/jq_03.png'),\n      url: 'http://myx.yeahtour.cn'\n    },\n    {\n      name: '皇澤寺',\n      img: require('@/assets/scenic/jq_05.png'),\n      url: 'http://hzs.yeahtour.cn'\n    },\n    {\n      name: '劍門關',\n      img: require('@/assets/scenic/jq_09.png'),\n      url: 'http://jmg.yeahtour.cn'\n    },\n    {\n      name: '千佛崖',\n      img: require('@/assets/scenic/jq_10.png'),\n      url: 'http://qfy.yeahtour.cn'\n    },\n    {\n      name: '龍門閣',\n      img: require('@/assets/scenic/jq_01.png'),\n      url: 'http://lmg.yeahtour.cn'\n    },\n    {\n      name: '水磨溝',\n      img: require('@/assets/scenic/jq_02.png'),\n      url: 'http://smg.yeahtour.cn'\n    },\n    {\n      name: '東河口',\n      img: require('@/assets/scenic/jq_06.png'),\n      url: 'http://dhk.yeahtour.cn'\n    },\n    {\n      name: '唐家河',\n      img: require('@/assets/scenic/jq_07.png'),\n      url: 'http://tjh.yeahtour.cn'\n    },\n    {\n      name: '梨博園',\n      img: require('@/assets/scenic/jq_08.png'),\n      url: 'http://lby.yeahtour.cn'\n    },\n    {\n      name: '米倉山大峽谷',\n      img: require('@/assets/scenic/jq_12.png'),\n      url: 'http://dxg.yeahtour.cn'\n    },\n    {\n      name: '鼓城山',\n      img: require('@/assets/scenic/jq_13.png'),\n      url: 'http://mcs.yeahtour.cn'\n    },\n    {\n      name: '卡爾城',\n      img: require('@/assets/scenic/jq_11.png'),\n      url: 'http://kec.yeahtour.cn'\n    },\n    {\n      name: '旺蒼縣文化館',\n      img: require('@/assets/scenic/jq_14.png'),\n      url: 'http://wcwhg.yeahtour.cn'\n    },\n    {\n      name: '旺蒼縣圖書館',\n      img: require('@/assets/scenic/jq_15.png'),\n      url: 'http://wctsg.yeahtour.cn'\n    },\n    {\n      name: '蒼溪縣文化館',\n      img: require('@/assets/scenic/jq_16.png'),\n      url: 'http://cxwhg.yeahtour.cn'\n    },\n    {\n      name: '蒼溪縣圖書館',\n      img: require('@/assets/scenic/jq_17.png'),\n      url: 'http://cxtsg.yeahtour.cn'\n    },\n    {\n      name: '劍閣縣圖書館',\n      img: require('@/assets/scenic/jq_18.png'),\n      url: 'http://jgtsg.yeahtour.cn'\n    },\n    {\n      name: '荊州園博園',\n      img: require('@/assets/scenic/jq_19.png'),\n      url: 'http://www.jzyby.com'\n    }\n  ],\n  toolsList: [\n    {\n      img: require('@/assets/tools/01.png'),\n      url: 'http://10.8.13.68'\n    },\n    {\n      img: require('@/assets/tools/02.png'),\n      url: 'https://10.8.1.221:31199'\n    },\n    {\n      img: require('@/assets/tools/03.png'),\n      url: 'http://git.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/04.png'),\n      url: 'http://prod-grafana.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/05.png'),\n      url: 'http://grafana-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/06.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/07.png'),\n      url: 'http://10.8.13.13/zabbix/'\n    },\n    {\n      img: require('@/assets/tools/08.png'),\n      url: 'http://rancher.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/09.png'),\n      url: 'https://10.8.11.60:8443'\n    },\n    {\n      img: require('@/assets/tools/10.png'),\n      url: 'http://minio-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/11.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/12.png'),\n      url: 'http://reg.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/13.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/14.png'),\n      url: 'http://10.8.11.55:8080/'\n    },\n    {\n      img: require('@/assets/tools/15.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/16.png'),\n      url: 'http://grafana-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/17.png'),\n      url: 'https://redash-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/18.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/19.png'),\n      url: 'http://**************:8069/'\n    },\n    {\n      img: require('@/assets/tools/20.png'),\n      url: ''\n    }\n  ],\n  footer: {\n    company: '「環球數科集團」',\n    copyright: '版權所有',\n    tips: '賬號'\n  }\n}", "export default {\n  link: 'No link',\n  title: 'Basic ability',\n  navList:['Basic ability','Industry + cloud','Operational tool','Cooperation scenic spot'],\n  wrapList: [\n    {\n      title: 'Basic ability',\n      projectList: [\n        // 数据分析\n        {\n          style: 's1',\n          title: 'Data and Analysis',\n          content: 'It provides a comprehensive intelligent platform for database query, large-scale calculation, data reports and other services to improve the efficiency of customer data analysis.',\n          logoList: [\n            // 灵狐\n            {\n              name: 'DataFox',\n              img: require('@/assets/logo/灵狐.png'),\n              // url: 'https://datafox.shukeyun.com/#/',\n              iconList: [],\n            },\n            // 分析引擎\n            {\n              name: 'Big data\\nanalysis engine',\n              img: require('@/assets/logo/分析引擎.png'),\n              // url: 'https://redash-prod.shukeyun.com/',\n              iconList: [],\n            },\n          ]\n        },\n        // 支付安全\n        {\n          style: 's2',\n          title: 'Payment and Security',\n          content: 'Combined with blockchain technology, ensure the security of transaction settlement, and effectively integrate the fund settlement of scenic spots and tourism-related enterprises.',\n          logoList: [\n            // 智旅链\n            {\n              name: 'Wisdom brigade chain',\n              img: require('@/assets/logo/智旅链.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Blockchain\\nservice\\nplatform',\n                  img: require('@/assets/icon/区块链服务平台.png'),\n                  // url: 'https://prod.shukeyun.com/blockchain/homepage/#',\n                },\n                {\n                  name: 'Data\\nregister',\n                  img: require('@/assets/icon/数据存证.png'),\n                  url: 'https://prod.shukeyun.com/blockchain/evidence-ui/#/HomeIndex',\n                },\n                {\n                  name: 'BDS\\nblockchain\\nbrowser',\n                  img: require('@/assets/icon/BDS 区块链浏览器.png'),\n                  url: 'https://explorer.zhilvlian.com',\n                },\n                {\n                  name: 'BDS\\nNFT\\nbrowser',\n                  img: require('@/assets/icon/BDS NFT 浏览器.png'),\n                  url: 'https://nft.zhilvlian.com/#/DigitalCollection',\n                },\n                {\n                  name: 'Digital\\ncollection\\nH5',\n                  img: require('@/assets/icon/数字藏品 H5.png'),\n                  url: 'https://nft.zhilvlian.com/blockchain/h5/#/',\n                },\n                {\n                  name: 'Digital\\ncollection\\nbackend',\n                  img: require('@/assets/icon/数字藏品管理后台.png'),\n                  url: 'https://nft.zhilvlian.com/blockchain/management/#/',\n                },\n              ],\n            },\n            // 云联网络\n            {\n              name: 'Cloud network',\n              img: require('@/assets/logo/云联网络.png'),\n              url: 'https://www.upaypal.com',\n              iconList: [],\n            },\n          ]\n        },\n      ]\n    },\n    {\n      title: 'Industry + cloud',\n      projectList: [\n        // 生态监管\n        {\n          style: 's3',\n          title: 'Ecology and Regulation',\n          content: 'Satellite communications, big data and other technologies should be integrated to build an all-dimensional and multi-tiered ecological monitoring platform and improve emergency response efficiency.',\n          logoList: [\n            // 监管\n            {\n              name: 'Smart tourism\\nsupervision platform',\n              img: require('@/assets/logo/朝天旅游.png'),\n              url: 'https://prod.shukeyun.com/data/dashboard/#/?id=zly_01_01',\n              iconList: [],\n            },\n            // 监管\n            {\n              name: 'Ecological monitoring\\ndisaster warning\\nmanagement platform',\n              img: require('@/assets/logo/生态监测.png'),\n              url: 'http://yanjinghe.zhlyjg.cn',\n              iconList: [],\n            },\n            // 森林防火\n            {\n              name: 'Forest fire\\nmonitoring warning\\ncommand system',\n              img: require('@/assets/logo/森林防火.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Data\\nscreen',\n                  img: require('@/assets/icon/数据大屏.png'),\n                  url: 'https://slfh.shengtaiyun.com/',\n                },\n                {\n                  name: 'Backend',\n                  img: require('@/assets/icon/管理后台.png'),\n                  url: 'https://slfh.shengtaiyun.com/management',\n                },\n                {\n                  name: 'APP\\ndownload',\n                  img: require('@/assets/qrCode/森林防火.png'),\n                  code: require('@/assets/qrCode/森林防火.png'),\n                  url: '',\n                },\n              ],\n            },\n          ]\n        },\n        // 城市管理\n        {\n          style: 's4',\n          title: 'Cities and Management',\n          content: 'Based on the omni-domain digital base, it uses the digital-real integration engine to provide more convenient and fast management and bring users a more comfortable \"digital\" life experience.',\n          logoList: [\n            // 车无忌\n            {\n              name: 'Car Wuji',\n              img: require('@/assets/logo/车无忌.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Backend',\n                  img: require('@/assets/icon/管理后台_.png'),\n                  url: 'http://www.parkingfree.cn/o2o-admin',\n                },\n                {\n                  name: 'Property\\nbackground',\n                  img: require('@/assets/icon/物业后台.png'),\n                  url: 'http://www.parkingfree.cn/o2o-cp',\n                }\n              ],\n            },\n            // 智慧社区\n            {\n              name: 'Smart\\ncommunity\\nsystem',\n              img: require('@/assets/logo/印象石灰.png'),\n              url: 'https://www.shgzsq.com/',\n              iconList: [],\n            },\n            // 母婴室\n            {\n              name: 'Smart\\nmother and baby\\nroom',\n              img: require('@/assets/logo/智慧母婴室.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Operations\\ncenter',\n                  img: require('@/assets/icon/运营中心.png'),\n                  url: 'http://mng.quanzilx.com',\n                },\n                {\n                  name: 'Supervision\\ncenter',\n                  img: require('@/assets/icon/监管中心.png'),\n                  url: 'http://jgf.quanzilx.com',\n                },\n                {\n                  name: 'Backend\\ncenter',\n                  img: require('@/assets/icon/管理中心.png'),\n                  url: 'http://my.quanzilx.com',\n                },\n                {\n                  name: 'Map\\nof mother\\nand maby\\nroom',\n                  img: require('@/assets/qrCode/母婴室.png'),\n                  code: require('@/assets/qrCode/母婴室.png'),\n                  url: '',\n                }\n              ],\n            },\n          ]\n        },\n        // 文化旅游\n        {\n          style: 's5',\n          title: 'Culture and Tourism',\n          content: 'Will go deep into the tourism industry, provide solutions for the digital construction and operation of tourism destinations, and build a cloud service platform for the whole scene destinations.',\n          logoList: [\n            // 途途\n            {\n              name: 'TUTU',\n              img: require('@/assets/logo/途途.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Tutubanyou',\n                  img: require('@/assets/qrCode/途途.png'),\n                  code: require('@/assets/qrCode/途途.png'),\n                  url: '',\n                },\n                {\n                  name: 'Counting\\nhomo\\nsapiens\\nTUTU',\n                  img: require('@/assets/icon/tutu.png'),\n                  url: 'https://prod.shukeyun.com/meta/chat/',\n                },\n                {\n                  name: 'Backend',\n                  img: require('@/assets/icon/物业后台.png'),\n                  url: 'https://meta.zhilvlian.com/management/welcome',\n                }\n              ],\n            },\n            // 智慧文旅\n            {\n              name: 'Wisdom cultural tourism',\n              img: require('@/assets/logo/智慧文旅.png'),\n              url: 'https://hly.net/#/',\n              iconList: [\n                // // 慧旅云\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/慧旅云.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=xZZLVvBpD79TQVxPWOnd',\n                // },\n                // // 慧景云\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/慧景云.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=QMMS89YvTZ0Qs5sRWXFL&tk=null&scenicCode=yndl',\n                // },\n                // // 易旅通\n                // {\n                //   name: '',\n                //   img: require('@/assets/logo/易旅通.png'),\n                //   url: 'https://prod.shukeyun.com/cas/login/#/login?appId=yc3fvV45yEWablblgUoq',\n                // },\n                // // 易旅宝\n                // {\n                //   name: 'Yilvbao',\n                //   img: require('@/assets/qrCode/易旅宝.png'),\n                //   code: require('@/assets/qrCode/易旅宝.png'),\n                //   url: '',\n                // },\n              ]\n            },\n            // 检票平台\n            {\n              name: 'Check-in platform',\n              img: require('@/assets/logo/检票平台.png'),\n              url: '',\n              iconList: [\n                {\n                  name: 'Check-in\\nplatform',\n                  img: require('@/assets/qrCode/检票.png'),\n                  code: require('@/assets/qrCode/检票.png'),\n                  url: '',\n                }\n              ],\n            },\n          ]\n        }\n      ]\n    },\n  ],\n  scenicList: [\n    {\n      name: 'Zengjiashan',\n      img: require('@/assets/scenic/jq_04.png'),\n      url: 'http://zjs.yeahtour.cn'\n    },\n    {\n      name: 'Mingyuexia',\n      img: require('@/assets/scenic/jq_03.png'),\n      url: 'http://myx.yeahtour.cn'\n    },\n    {\n      name: 'Huangze temple',\n      img: require('@/assets/scenic/jq_05.png'),\n      url: 'http://hzs.yeahtour.cn'\n    },\n    {\n      name: 'Jianmenguan',\n      img: require('@/assets/scenic/jq_09.png'),\n      url: 'http://jmg.yeahtour.cn'\n    },\n    {\n      name: 'Thousand-buddha cliff',\n      img: require('@/assets/scenic/jq_10.png'),\n      url: 'http://qfy.yeahtour.cn'\n    },\n    {\n      name: 'Longmen pavilion',\n      img: require('@/assets/scenic/jq_01.png'),\n      url: 'http://lmg.yeahtour.cn'\n    },\n    {\n      name: 'Shuimogou',\n      img: require('@/assets/scenic/jq_02.png'),\n      url: 'http://smg.yeahtour.cn'\n    },\n    {\n      name: 'Donghekou',\n      img: require('@/assets/scenic/jq_06.png'),\n      url: 'http://dhk.yeahtour.cn'\n    },\n    {\n      name: 'Tangjia river',\n      img: require('@/assets/scenic/jq_07.png'),\n      url: 'http://tjh.yeahtour.cn'\n    },\n    {\n      name: 'Pear expo garden',\n      img: require('@/assets/scenic/jq_08.png'),\n      url: 'http://lby.yeahtour.cn'\n    },\n    {\n      name: 'Micangshan grand canyon',\n      img: require('@/assets/scenic/jq_12.png'),\n      url: 'http://dxg.yeahtour.cn'\n    },\n    {\n      name: 'Guchengshan',\n      img: require('@/assets/scenic/jq_13.png'),\n      url: 'http://mcs.yeahtour.cn'\n    },\n    {\n      name: 'Carl city',\n      img: require('@/assets/scenic/jq_11.png'),\n      url: 'http://kec.yeahtour.cn'\n    },\n    {\n      name: 'Wangcang county cultural center',\n      img: require('@/assets/scenic/jq_14.png'),\n      url: 'http://wcwhg.yeahtour.cn'\n    },\n    {\n      name: 'Wangcang county library',\n      img: require('@/assets/scenic/jq_15.png'),\n      url: 'http://wctsg.yeahtour.cn'\n    },\n    {\n      name: 'Cangxi county cultural center',\n      img: require('@/assets/scenic/jq_16.png'),\n      url: 'http://cxwhg.yeahtour.cn'\n    },\n    {\n      name: 'Cangxi county library',\n      img: require('@/assets/scenic/jq_17.png'),\n      url: 'http://cxtsg.yeahtour.cn'\n    },\n    {\n      name: 'Jiangue county library',\n      img: require('@/assets/scenic/jq_18.png'),\n      url: 'http://jgtsg.yeahtour.cn'\n    },\n    {\n      name: 'Jingzhou garden expo garden',\n      img: require('@/assets/scenic/jq_19.png'),\n      url: 'http://www.jzyby.com'\n    }\n  ],\n  toolsList: [\n    {\n      img: require('@/assets/tools/01.png'),\n      url: 'http://10.8.13.68'\n    },\n    {\n      img: require('@/assets/tools/02.png'),\n      url: 'https://10.8.1.221:31199'\n    },\n    {\n      img: require('@/assets/tools/03.png'),\n      url: 'http://git.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/04.png'),\n      url: 'http://prod-grafana.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/05.png'),\n      url: 'http://grafana-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/06.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/07.png'),\n      url: 'http://10.8.13.13/zabbix/'\n    },\n    {\n      img: require('@/assets/tools/08.png'),\n      url: 'http://rancher.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/09.png'),\n      url: 'https://10.8.11.60:8443'\n    },\n    {\n      img: require('@/assets/tools/10.png'),\n      url: 'http://minio-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/11.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/12.png'),\n      url: 'http://reg.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/13.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/14.png'),\n      url: 'http://10.8.11.55:8080/'\n    },\n    {\n      img: require('@/assets/tools/15.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/16.png'),\n      url: 'http://grafana-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/17.png'),\n      url: 'https://redash-prod.shukeyun.com'\n    },\n    {\n      img: require('@/assets/tools/18.png'),\n      url: ''\n    },\n    {\n      img: require('@/assets/tools/19.png'),\n      url: 'http://**************:8069/'\n    },\n    {\n      img: require('@/assets/tools/20.png'),\n      url: ''\n    }\n  ],\n  footer: {\n    company: '[Global Digetal Technology Group CO.,Ltd.]',\n    copyright: 'All rights reserved',\n    tips: 'Account'\n  }\n}", "import { defineStore } from 'pinia'\nimport zh from \"./zh\";\nimport en from \"./en\";\n\nexport const langStore = defineStore('langStore', {\n  state: () => (!!localStorage.getItem('isLang')?{...en}:{...zh})\n})", "import { render } from \"./Wrap.vue?vue&type=template&id=0b2100c2&scoped=true\"\nimport script from \"./Wrap.vue?vue&type=script&lang=js\"\nexport * from \"./Wrap.vue?vue&type=script&lang=js\"\n\nimport \"./Wrap.vue?vue&type=style&index=0&id=0b2100c2&lang=less&scoped=true\"\nimport \"./Wrap.vue?vue&type=style&index=1&id=0b2100c2&lang=less\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0b2100c2\"]])\n\nexport default __exports__", "<template>\n  <svg viewBox=\"0 0 24 24\" class=\"dark-icon\" data-v-3de418c2=\"\"><path d=\"M11.01 3.05C6.51 3.54 3 7.36 3 12a9 9 0 0 0 9 9c4.63 0 8.45-3.5 8.95-8c.09-.79-.78-1.42-1.54-.95A5.403 5.403 0 0 1 11.1 7.5c0-1.06.31-2.06.84-2.89c.45-.67-.04-1.63-.93-1.56z\" fill=\"currentColor\"></path></svg>\n</template>", "import { render } from \"./dark.vue?vue&type=template&id=9949a34e\"\nconst script = {}\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <svg viewBox=\"0 0 24 24\" class=\"light-icon\" data-v-3de418c2=\"\"><path d=\"M6.05 4.14l-.39-.39a.993.993 0 0 0-1.4 0l-.01.01a.984.984 0 0 0 0 1.4l.39.39c.39.39 1.01.39 1.4 0l.01-.01a.984.984 0 0 0 0-1.4zM3.01 10.5H1.99c-.55 0-.99.44-.99.99v.01c0 .***********.99H3c.56.01 1-.43 1-.98v-.01c0-.56-.44-1-.99-1zm9-9.95H12c-.56 0-1 .44-1 .99v.96c0 .***********.99H12c.56.01 1-.43 1-.98v-.97c0-.55-.44-.99-.99-.99zm7.74 3.21c-.39-.39-1.02-.39-1.41-.01l-.39.39a.984.984 0 0 0 0 1.4l.01.01c.39.39 1.02.39 1.4 0l.39-.39a.984.984 0 0 0 0-1.4zm-1.81 15.1l.39.39a.996.996 0 1 0 1.41-1.41l-.39-.39a.993.993 0 0 0-1.4 0c-.4.4-.4 1.02-.01 1.41zM20 11.49v.01c0 .***********.99H22c.55 0 .99-.44.99-.99v-.01c0-.55-.44-.99-.99-.99h-1.01c-.55 0-.99.44-.99.99zM12 5.5c-3.31 0-6 2.69-6 6s2.69 6 6 6s6-2.69 6-6s-2.69-6-6-6zm-.01 16.95H12c.55 0 .99-.44.99-.99v-.96c0-.55-.44-.99-.99-.99h-.01c-.55 0-.99.44-.99.99v.96c0 .***********.99zm-7.74-3.21c.39.39 1.02.39 1.41 0l.39-.39a.993.993 0 0 0 0-1.4l-.01-.01a.996.996 0 0 0-1.41 0l-.39.39c-.38.4-.38 1.02.01 1.41z\" fill=\"currentColor\"></path></svg>\n</template>", "import { render } from \"./light.vue?vue&type=template&id=1bad1530\"\nconst script = {}\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <div>\n    <b>文</b>\n    <sub>A</sub>\n  </div>\n</template>", "import { render } from \"./langZ.vue?vue&type=template&id=0dd143d8\"\nconst script = {}\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "<template>\n  <div>\n    <b>A</b>\n    <sub>文</sub>\n  </div>\n</template>", "import { render } from \"./langE.vue?vue&type=template&id=e770251a\"\nconst script = {}\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { render } from \"./App.vue?vue&type=template&id=38d670af\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=38d670af&lang=less\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\WXWork\\\\****************\\\\Cache\\\\File\\\\2025-03\\\\demo-master\\\\demo-master\\\\node_modules\\\\vue-loader\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createApp } from 'vue'\nimport App from './App.vue'\nimport { createPinia } from 'pinia';\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport 'element-plus/theme-chalk/display.css'\nimport 'element-plus/theme-chalk/dark/css-vars.css'\n\ncreateApp(App).use(createPinia()).use(ElementPlus).mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "__webpack_require__.p = \"\";", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkdemo_vue3_0\"] = self[\"webpackChunkdemo_vue3_0\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(7410); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["class", "_createElementVNode", "$data", "_hoisted_1", "src", "_imports_0", "alt", "onClick", "$options", "_hoisted_2", "_createElementBlock", "_Fragment", "navList", "item", "index", "key", "_createVNode", "_Transition", "name", "_component_langE", "_component_langZ", "_component_dark", "_component_light", "_component_el_dropdown", "dropdown", "_component_el_dropdown_menu", "_createBlock", "_component_el_dropdown_item", "divided", "_component_el_icon", "_component_Menu", "wrapList", "wrapItem", "wrapIndex", "_hoisted_4", "title", "projectList", "projectItem", "projectIndex", "_component_wrap", "style", "_hoisted_5", "_hoisted_6", "_hoisted_7", "toolsList", "img", "url", "_hoisted_9", "_hoisted_10", "_hoisted_11", "scenicList", "_hoisted_12", "_hoisted_14", "_hoisted_3", "$props", "content", "logoList", "logoItem", "logoIndex", "iconList", "length", "iconItem", "iconIndex", "link", "require", "code", "footer", "company", "copyright", "tips", "langStore", "defineStore", "state", "localStorage", "getItem", "en", "zh", "props", "data", "lang", "methods", "toUrl", "window", "open", "this", "$notify", "customClass", "dangerouslyUseHTMLString", "message", "duration", "storeToRefs", "type", "__exports__", "viewBox", "d", "fill", "script", "components", "wrap", "<PERSON><PERSON>", "langZ", "langE", "dark", "light", "isLang", "isDark", "useDark", "ishead", "headerDown", "date", "created", "location", "search", "addEventListener", "getTop", "mounted", "transition", "Date", "getFullYear", "setDark", "setItem", "setLang", "$state", "enLang", "zhLang", "get<PERSON>le", "className", "classIndex", "document", "getElementsByClassName", "pageYOffset", "documentElement", "scrollTop", "body", "scrollToTargetAdjusted", "element", "scrollTo", "top", "getBoundingClientRect", "behavior", "dom", "IntersectionObserver", "entries", "isIntersecting", "setAttribute", "observe", "render", "createApp", "App", "use", "createPinia", "ElementPlus", "mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "Object", "keys", "every", "splice", "r", "n", "getter", "__esModule", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "e", "obj", "prop", "prototype", "hasOwnProperty", "p", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "for<PERSON>ach", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}
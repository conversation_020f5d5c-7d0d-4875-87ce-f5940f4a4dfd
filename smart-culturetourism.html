﻿<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width,initial-scale=1" />
		<meta name="renderer" content="webkit" />
		<meta name="force-rendering" content="webkit" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta name="format-detection" content="telephone=no" />
		<meta name="generator" content="supercache" />
		<meta name="generated" content="1742379737" />
		<link
			rel="shortcut icon"
			type="image/x-icon"
			href="uploads/sites/1012/2022/11/09c91cee4d1de7c7f21689b82b9221c2.ico" />
		<title>智慧文旅 - 環球數科集團有限公司</title>
		<script
			class="reload-js"
			src="dist/visual/sites/1012/global.js?ver=1739755198397-12152"></script>
		<script id="_CONFIG_">
			window["_CONFIG_"]["sidebar"] = ""
			window["_CONFIG_"]["current"] = { module: "page", type: "page", id: 5 }
		</script>
		<link rel="stylesheet" href="dist/theme/static/css/core.css?ver=12152" />
		<link rel="stylesheet" href="dist/theme/static/css/main.css?ver=12152" />
		<link
			rel="stylesheet"
			href="dist/theme/static/css/main.media.css?ver=12152" />
		<script src="dist/theme/static/js/core.js?ver=12152"></script>
		<script src="dist/theme/static/js/main.js?ver=12152"></script>
		<link
			rel="stylesheet"
			class="reload-css"
			href="dist/visual/sites/1012/style.css?ver=1739755198397-12152" />

		<link rel="dns-prefetch" href="index.htm" />
		<link rel="dns-prefetch" href="//static2.xunxiang.site" />
		<link rel="canonical" href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html" />

		<style class="custom-css-code">
			/* 陰影 */
			.custom-yy {
				box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
			}
			/* 案例陰影 */
			.custom-al {
				box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
				-webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
				-moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
			}
		</style>
	</head>
	<body
		class="layout-full-width header-type-fixed header-type-mobile-default responsive">
		<div class="App loading">
			<div class="Page">
				<div class="Page-header">
					<div
						class="Page-slot--template-top"
						template_type="global"
						template_position="template-top"
						template_id="54">
						<div
							node-id="id-71-ompqcelcmx"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-71-ompqcelcmx">
								@media only screen and (max-width: 767px) {
									[node-id="id-71-ompqcelcmx"] {
										border-bottom-color: #eeeeee;
										border-bottom-width: 1px;
										border-style: solid;
									}
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-71-ompqcelcmx",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-85-vzlyqjjsj0"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-12 cc-col-xl-12 cc-col-lg3-12 cc-col-lg2-12 cc-col-lg-12 cc-col-md-12 cc-col-sm-12 cc-col-xs-12">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-85-vzlyqjjsj0",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-38-gg2lbbb6vg--wrapper">
									<div
										node-id="id-38-gg2lbbb6vg"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-38-gg2lbbb6vg",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-66-j6pl1uvvjh"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-66-j6pl1uvvjh",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-25-gzeawqw0td"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-25-gzeawqw0td",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-57-f1cjboih9d--wrapper">
													<style style-id="id-57-f1cjboih9d">
														@media only screen and (min-width: 768px) {
															[node-id="id-57-f1cjboih9d"] {
																padding-top: 10px;
																padding-right: 10px;
																padding-bottom: 10px;
																padding-left: 10px;
															}
														}
														[node-id="id-57-f1cjboih9d"] .cc-textblock__body {
															padding: 0px;
														}
													</style>
													<div
														node-id="id-57-f1cjboih9d"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #0064d8"
																	>&nbsp;<span class="mark-fapkw"
																		><!-- . --><span
																			contenteditable="false"
																			class="mark-fapk fas fa-home"></span
																	></span> </span
																>&nbsp;<span style="font-size: 14px"
																	>Welcome to Digital Origin (Hong Kong) Limited!</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-57-f1cjboih9d",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-93-zii776eer7"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-93-zii776eer7",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-45-dplccc0zbv"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-45-dplccc0zbv",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-65-txvzzlovo8--wrapper">
													<style style-id="id-65-txvzzlovo8">
														@media only screen and (min-width: 768px) {
															[node-id="id-65-txvzzlovo8"] {
																padding-top: 10px;
																padding-right: 10px;
																padding-bottom: 10px;
																padding-left: 10px;
															}
														}
														[node-id="id-65-txvzzlovo8"] .cc-textblock__body {
															padding: 10px;
														}
													</style>
													<div
														node-id="id-65-txvzzlovo8"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #0064d8"
																	> <span class="mark-fapkw"
																		><!-- . --><span
																			contenteditable="false"
																			class="mark-fapk fas fa-home"></span
																	></span> </span
																> <span style="font-size: 14px"
																	>歡迎來到環球數科！</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-65-txvzzlovo8",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div
								node-id="id-24-hm9hozmolg"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-12 cc-col-xl-12 cc-col-lg3-12 cc-col-lg2-12 cc-col-lg-12 cc-col-md-12 cc-col-sm-12 cc-col-xs-12">
								<style style-id="id-24-hm9hozmolg">
									@media only screen and (max-width: 767px) {
										[node-id="id-24-hm9hozmolg"] {
											padding-right: 10px;
										}
									}
									@media only screen and (min-width: 1360px) {
										[node-id="id-24-hm9hozmolg"] {
											padding-left: 100px;
										}
									}
								</style>
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-24-hm9hozmolg",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-62-zyiiii5rej--wrapper">
									<div
										node-id="id-62-zyiiii5rej"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<style style-id="id-62-zyiiii5rej">
											@media only screen and (max-width: 767px) {
												[node-id="id-62-zyiiii5rej"] {
													padding-bottom: 10px;
													padding-top: 10px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-62-zyiiii5rej",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-61-o4kc47k5fb"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-61-o4kc47k5fb",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-12-w5d4dw8bbk"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-12-w5d4dw8bbk",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-45-lnnzwfqi5f--wrapper">
													<style style-id="id-45-lnnzwfqi5f">
														[node-id="id-45-lnnzwfqi5f"].cc-button {
															overflow: hidden;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button .slot-main {
															background-color: rgba(255, 255, 255, 1);
															color: #333333;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button--line
															.slot-main {
															border-color: rgba(255, 255, 255, 1);
															color: #333333;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button .slot-main {
															border: 1px solid #eeeeee;
														}
														[node-id="id-45-lnnzwfqi5f"].cc-button
															.slot-main:hover {
															border: 1px solid #0064d8;
														}
													</style>
													<div
														node-id="id-45-lnnzwfqi5f"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--right">
														<div class="slot-main" id="id-45-lnnzwfqi5f">
															<p><span style="font-size: 14px">CN</span></p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-45-lnnzwfqi5f",
																options: {
																	handler: {
																		action: "open",
																		options: { target: "_self", url: "\/index.htm" }
																	},
																	btnBorder: "yes",
																	"hover-animation": [],
																	align: "right",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-62-o1la61yy10"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-15 cc-col-xl-15 cc-col-lg3-15 cc-col-lg2-15 cc-col-lg-15 cc-col-md-15 cc-col-sm-15 cc-col-xs-0">
												<style style-id="id-62-o1la61yy10">
													@media only screen and (min-width: 768px) {
														[node-id="id-62-o1la61yy10"] {
															padding-right: 20px;
															padding-left: 20px;
														}
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-62-o1la61yy10",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-23-h94dm9wtdk--wrapper">
													<style style-id="id-23-h94dm9wtdk">
														[node-id="id-23-h94dm9wtdk"] {
															--border-color: var(--border-color);
															font-size: 14px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search__field {
															color: #000;
														}
														[node-id="id-23-h94dm9wtdk"]
															.cc-search__field:hover,
														[node-id="id-23-h94dm9wtdk"]
															.cc-search__field:focus {
															border-color: var(--border-color);
															font-size: 14px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search__field {
															background-color: ;
															height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .choices__inner {
															height: 38px;
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-input-append {
															background-color: rgba(0, 129, 249, 1);
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"] .cc-search--button {
															color: rgba(255, 255, 255, 1);
														}
														[node-id="id-23-h94dm9wtdk"]
															.cc-form--input__append
															.cc-input-append {
															padding: 0 10px;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search
															.cc-input-append
															.cc-search--button
															img {
															vertical-align: middle;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search
															.cc-input-append {
															line-height: 38px;
														}
														[node-id="id-23-h94dm9wtdk"].cc-search .cc-choices {
															min-width: 50px;
														}
													</style>
<!--													<div-->
<!--														node-id="id-23-h94dm9wtdk"-->
<!--														node-type="search"-->
<!--														class="cc-search cc-search&#45;&#45;width__full">-->
<!--														<div class="lay-fx">-->
<!--															<div-->
<!--																class="cc-form&#45;&#45;input__wrapper cc-form&#45;&#45;input__append">-->
<!--																<input-->
<!--																	class="cc-form&#45;&#45;input cc-search__field"-->
<!--																	type="text"-->
<!--																	placeholder="搜索文章、頁面" />-->
<!--																<div class="cc-input-append">-->
<!--																	<button-->
<!--																		class="cc-search&#45;&#45;button"-->
<!--																		action="https://www.hqshuke.com/search">-->
<!--																		<img-->
<!--																			src="uploads/sites/1012/2022/12/5981d2b1347e3df2eb51c919f1b7e57b.png"-->
<!--																			width="26"-->
<!--																			height="26" />-->
<!--																	</button>-->
<!--																</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->

													<script>
														;(function () {
															useComponent("search").default({
																id: "id-23-h94dm9wtdk",
																options: {
																	__note: "",
																	type: "page",
																	"select-min-width": "50px",
																	"select-ellipsis": "no",
																	open: "yes",
																	"width-full": "yes",
																	"placeholder-text":
																		"\u641c\u7d22\u6587\u7ae0\u3001\u9875\u9762",
																	"placeholder-text-color-enable": "no",
																	"placeholder-text-color": "#C2C2C2",
																	"show-search-icon": "yes",
																	"search-icon-src":
																		"\/\/static2.xunxiang.site\/uploads\/sites\/1012\/2022\/12\/5981d2b1347e3df2eb51c919f1b7e57b.png",
																	"btn-text": "\u641c\u7d22",
																	"btn-color": "rgba(255, 255, 255, 1)",
																	"btn-bg-color": "rgba(0, 129, 249, 1)",
																	"font-size": "14px",
																	"btn-pad": "10px",
																	height: "38px",
																	"border-color": "",
																	"bg-color": "",
																	"init-animation": [],
																	"hover-animation": [],
																	"keyword-required": "no",
																	"search-text-color": "#000",
																	"border-color-hover": "",
																	"select-style-enable": "no",
																	"select-style": [],
																	"present-hover-select-style": []
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-73-me3uwghnqw"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-5 cc-col-xl-5 cc-col-lg3-5 cc-col-lg2-5 cc-col-lg-5 cc-col-md-5 cc-col-sm-5 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-73-me3uwghnqw",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-71-bj5bmz0t9e--wrapper">
													<style style-id="id-71-bj5bmz0t9e">
														[node-id="id-71-bj5bmz0t9e"].cc-button {
															overflow: hidden;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button .slot-main {
															background-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button--line
															.slot-main {
															border-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button .slot-main {
															border: 1px solid #ccc;
														}
														[node-id="id-71-bj5bmz0t9e"].cc-button
															.slot-main:hover {
															border: 1px solid #ccc;
														}
													</style>
													<div
														node-id="id-71-bj5bmz0t9e"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--left">
														<div class="slot-main" id="id-71-bj5bmz0t9e">
															<p>
																<span style="font-size: 14px">Login/Register</span>
															</p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-71-bj5bmz0t9e",
																options: {
																	handler: {
																		action: "open",
																		options: {
																			target: "_blank",
																			url: "https:\/\/prod.shukeyun.com\/cas\/login\/#\/login?appId=CvGwBHU2YiPygGGY5bMF"
																		}
																	},
																	btnBorder: "no",
																	"hover-animation": [],
																	align: "left",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-21-ki85qv186z"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-21-ki85qv186z",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-11-jx5af2kbms"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-5">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-11-jx5af2kbms",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-18-egqdv8ysrr--wrapper">
													<style style-id="id-18-egqdv8ysrr">
														[node-id="id-18-egqdv8ysrr"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-18-egqdv8ysrr"].cc-textblock
															.cc-textblock__body,
														[node-id="id-18-egqdv8ysrr"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-18-egqdv8ysrr"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p style="text-align: center">
																<span style="font-size: 14px"
																	><a href="en.html">CN</a></span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-18-egqdv8ysrr",
																options: []
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-61-pnaa7gq1ac"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-6">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-61-pnaa7gq1ac",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-14-ckz0a4xh0k--wrapper">
													<div
														node-id="id-14-ckz0a4xh0k"
														node-type="picture"
														class="cc-picture cc-picture--align__right">
														<img
															class="cc-picture--img"
															src="uploads/sites/1012/2022/12/b9942aad9d1138e0bb16b5303ba9fba2.png" />
													</div>

													<script>
														;(function () {
															useComponent("picture").default({
																id: "id-14-ckz0a4xh0k",
																options: {
																	handler: {
																		action: "modal",
																		options: {
																			close: "1",
																			autoPlay: "0",
																			template_id: 3,
																			width: "center"
																		}
																	}
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-62-v63z41dt68"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-0 cc-col-lg3-0 cc-col-lg2-0 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-13">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-62-v63z41dt68",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-18-nita1s00y0--wrapper">
													<style style-id="id-18-nita1s00y0">
														[node-id="id-18-nita1s00y0"].cc-button {
															overflow: hidden;
														}
														[node-id="id-18-nita1s00y0"].cc-button .slot-main {
															background-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main:hover {
															background-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button--line
															.slot-main {
															border-color: rgba(0, 129, 249, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button--line
															.slot-main:hover {
															border-color: rgba(0, 100, 216, 1);
															color: #ffffff;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main
															table
															td {
															padding-left: 12px;
															padding-right: 12px;
															padding-top: 12px;
															padding-bottom: 12px;
														}
														[node-id="id-18-nita1s00y0"].cc-button .slot-main {
															border: 1px solid #ccc;
														}
														[node-id="id-18-nita1s00y0"].cc-button
															.slot-main:hover {
															border: 1px solid #ccc;
														}
													</style>
													<div
														node-id="id-18-nita1s00y0"
														node-type="button"
														class="cc-button cc-button-- cc-button--large cc-button--square cc-button--right">
														<div class="slot-main" id="id-18-nita1s00y0">
															<p>Login/Register</p>
														</div>
													</div>
													<script>
														;(function () {
															useComponent("button").default({
																id: "id-18-nita1s00y0",
																options: {
																	handler: {
																		action: "open",
																		options: {
																			target: "_self",
																			url: "https:\/\/prod.shukeyun.com\/cas\/login\/#\/login?appId=CvGwBHU2YiPygGGY5bMF"
																		}
																	},
																	btnBorder: "no",
																	"hover-animation": [],
																	align: "right",
																	size: "large"
																}
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="Page-header--main default">
						<div class="Page-header--main__in container">
							<div class="Page-header--custommenu">
								<div class="Page-header--menu">
									<div
										node-id="id-13-hewiycbx4p"
										node-type="row"
										class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
										<script>
											;(function () {
												useComponent("row").default({
													id: "id-13-hewiycbx4p",
													options: {
														"full-width": "default",
														"adaption-height": "no",
														"background-video": "",
														"noheader-full-height": "no",
														"auto-flex": [],
														"auto-flex-enable": "no"
													}
												})
											})()
										</script>
										<div
											node-id="id-51-g757bjwmff"
											node-type="column"
											class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
											<script>
												;(function () {
													useComponent("column").default({
														id: "id-51-g757bjwmff",
														options: []
													})
												})()
											</script>
											<div
												class="cc-element--wrapper id-60-nkt0mtoceo--wrapper">
												<div
													node-id="id-60-nkt0mtoceo"
													node-type="menudropdown"
													class="cc-menudropdown">
													<div class="cc-menudropdown--header">
														<div class="cc-menudropdown--nav">
															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="./en.html"
																	target="_self">
																	Front Page
																</a>
															</div>

															
															<div class="cc-menudropdown--item current">
																<a
																	class="cc-menudropdown--link"
																	href="./smart-culturetourism.html"
																	target="_self">
																	Solution
																</a>
															</div>

														

															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="./industry-insights.html"
																	target="_self">
																	Industry Insights
																</a>
															</div>

															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="https://h292273.s806.ubn.cn/shukeyun/"
																	target="_self">
																	Experience Center
																</a>
															</div>
															<div class="cc-menudropdown--item">
																<a
																	class="cc-menudropdown--link"
																	href="./about-us.html"
																	target="_self">
																	Contact Us
																</a>
															</div>
															<div class="cc-menudropdown--item not-dropdown">
																<a
																	class="cc-menudropdown--link"
																	href="https://www.offertoday.com/hk/company/K_9_TTxML5dGyOKBV-VRFg=="
																	target="_self">
																	Careers
																</a>
															</div>
														</div>
													</div>
													<div class="cc-menudropdown--content">
														<style style-id="id-60-nkt0mtoceo">
															[node-id="id-60-nkt0mtoceo"] {
																--font-size: 16px;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item {
																height: 30px;
																line-height: 30px;
																padding: 0 16px;
																background-color: rgba(255, 255, 255, 0);
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--nav {
																text-align: right;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item:hover,
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item.current {
																background-color: rgba(255, 255, 255, 0);
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--link {
																color: #ffffff;
															}
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item:hover
																> .cc-menudropdown--link,
															[node-id="id-60-nkt0mtoceo"]
																.cc-menudropdown--item.current
																> .cc-menudropdown--link {
																color: #1064a9;
															}
															[node-id="id-60-nkt0mtoceo"].cc-menudropdown
																.cc-menudropdown--content
																.cc-menudropdown--dropdown {
																top: calc(30px + 0px);
																border-radius: 0px;
															}
														</style>
														<!-- <script>
															;(function () {
																useComponent("menudropdown").default({
																	id: "id-60-nkt0mtoceo",
																	options: {
																		trigger: "hover",
																		"item-style": "default",
																		"line-style": "left",
																		"arrow-slide-enable": "no",
																		"item-padding": "16px",
																		dropup: "no"
																	}
																})
															})()
														</script> -->
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__menu"
															style="height: 300px; background: #fff">
															<div
																node-id="id-12-j8tbbm9j12g"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-12-j8tbbm9j12g",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-11-0727ibql2p8"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-11-0727ibql2p8",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-10-etjn7sidor--wrapper">
																		<div
																			node-id="id-10-etjn7sidor"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-10-etjn7sidor",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-85-hfiq39ahcq"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-85-hfiq39ahcq">
																	[node-id="id-85-hfiq39ahcq"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-85-hfiq39ahcq",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-97-jx99g23xdw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-97-jx99g23xdw",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-75-mzk3xbbfxw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-75-mzk3xbbfxw",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-93-xtr08jgd1h--wrapper">
																		<div
																			node-id="id-93-xtr08jgd1h"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-93-xtr08jgd1h">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-93-xtr08jgd1h"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-93-xtr08jgd1h"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-93-xtr08jgd1h",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-36-ib1yit3br3"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-36-ib1yit3br3",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-54-fiy0nl2mnn"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-54-fiy0nl2mnn",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-17-ch11kyzknp--wrapper">
																						<style style-id="id-17-ch11kyzknp">
																							[node-id="id-17-ch11kyzknp"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-17-ch11kyzknp"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-17-ch11kyzknp"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-17-ch11kyzknp"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									關於我們
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-17-ch11kyzknp",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-45-c8m00gewhh--wrapper">
																						<style style-id="id-45-c8m00gewhh">
																							[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"] {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: #00b5ae;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: #009892;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #fff;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #fff;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #fff;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-45-c8m00gewhh"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #fff;
																							}
																							[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-45-c8m00gewhh"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-45-c8m00gewhh"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-45-c8m00gewhh"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-45-c8m00gewhh"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-45-c8m00gewhh"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#公司簡介">
																											<span
																												class="cc-menu--item__title">
																												公司簡介
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#業務佈局">
																											<span
																												class="cc-menu--item__title">
																												業務佈局
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#榮譽資質">
																											<span
																												class="cc-menu--item__title">
																												榮譽資質
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#企業文化">
																											<span
																												class="cc-menu--item__title">
																												企業文化
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E5%85%B3%E4%BA%8E%E6%88%91%E4%BB%AC.html#發展歷程">
																											<span
																												class="cc-menu--item__title">
																												發展歷程
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-45-c8m00gewhh",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "no",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-42-xnxqb4aurw"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-42-xnxqb4aurw",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-84-zcv04v0v34"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-84-zcv04v0v34">
																		[node-id="id-84-zcv04v0v34"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-84-zcv04v0v34"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-84-zcv04v0v34",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-37-hikpu6411f--wrapper">
																		<style style-id="id-37-hikpu6411f">
																			[node-id="id-37-hikpu6411f"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-37-hikpu6411f"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-37-hikpu6411f"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-37-hikpu6411f"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-37-hikpu6411f",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-20-bcpagusgsl--wrapper">
																		<style style-id="id-20-bcpagusgsl">
																			[node-id="id-20-bcpagusgsl"] {
																				padding-top: 40px;
																			}
																			[node-id="id-20-bcpagusgsl"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-20-bcpagusgsl"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-20-bcpagusgsl"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-20-bcpagusgsl"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					&ldquo;3+X&rdquo;業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82.html?vc_preview=yes"
																						>智慧城市</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a href="/通用解決方案">更多行業</a>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-20-bcpagusgsl",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-95-xggqlyl0zz"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-95-xggqlyl0zz">
																		[node-id="id-95-xggqlyl0zz"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-95-xggqlyl0zz",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-26-zwtg0k9kue"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-26-zwtg0k9kue">
																	[node-id="id-26-zwtg0k9kue"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-26-zwtg0k9kue",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-59-s3kf2yfb2k"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-59-s3kf2yfb2k",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-23-d7utcvzoho--wrapper">
																		<div
																			node-id="id-23-d7utcvzoho"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-23-d7utcvzoho">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-23-d7utcvzoho"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-23-d7utcvzoho"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-23-d7utcvzoho",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-21-n22ax2ejax"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-21-n22ax2ejax",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-37-kauo8fgndx"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-37-kauo8fgndx",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-82-nln8wu1qzq--wrapper">
																						<style style-id="id-82-nln8wu1qzq">
																							[node-id="id-82-nln8wu1qzq"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-82-nln8wu1qzq"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-82-nln8wu1qzq"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-82-nln8wu1qzq"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									行業解決方案
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-82-nln8wu1qzq",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-55-ttx2bdc15t--wrapper">
																						<style style-id="id-55-ttx2bdc15t">
																							[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"] {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-55-ttx2bdc15t"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-55-ttx2bdc15t"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-55-ttx2bdc15t"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-55-ttx2bdc15t"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-55-ttx2bdc15t"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li
																									class="cc-menu--item current">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html">
																											<span
																												class="cc-menu--item__title">
																												智慧文旅
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%95%B0%E5%AD%97%E6%99%AF%E5%8C%BA.html">
																													<span
																														class="cc-menu--item__title">
																														數字景區
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%99%BA%E8%83%BD%E8%90%A5%E9%94%80.html">
																													<span
																														class="cc-menu--item__title">
																														智能營銷
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85/%E6%99%BA%E6%85%A7%E7%9B%91%E7%AE%A1.html">
																													<span
																														class="cc-menu--item__title">
																														智慧監管
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82-1.html">
																											<span
																												class="cc-menu--item__title">
																												城市服務
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E7%A4%BE%E5%8C%BA.html">
																													<span
																														class="cc-menu--item__title">
																														智慧社區
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%85%AC%E4%BA%A4.html">
																													<span
																														class="cc-menu--item__title">
																														智慧公交
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%9C%BA%E7%AB%99.html">
																													<span
																														class="cc-menu--item__title">
																														智慧場站
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E5%9F%8E%E5%AE%89%E5%BA%94%E6%80%A5.html">
																													<span
																														class="cc-menu--item__title">
																														城安應急
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82/%E6%99%BA%E6%85%A7%E5%85%85%E7%94%B5%E6%A1%A9.html">
																													<span
																														class="cc-menu--item__title">
																														智慧充電樁
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html">
																											<span
																												class="cc-menu--item__title">
																												智慧生態
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E7%94%9F%E6%80%81%E7%9B%91%E6%B5%8B.html">
																													<span
																														class="cc-menu--item__title">
																														生態監測
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1.html">
																													<span
																														class="cc-menu--item__title">
																														災害預警
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81/%E6%A3%AE%E6%9E%97%E9%98%B2%E7%81%AB.html">
																													<span
																														class="cc-menu--item__title">
																														森林防火
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-55-ttx2bdc15t",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-51-jznnd6t0u6"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-51-jznnd6t0u6",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-5-50p7sikk6a--wrapper">
																		<div
																			node-id="id-5-50p7sikk6a"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-5-50p7sikk6a">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-5-50p7sikk6a"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-5-50p7sikk6a"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-5-50p7sikk6a",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-6-qb7gcin1mn"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-6-qb7gcin1mn",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-7-k0b79demsfo"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-7-k0b79demsfo",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-8-346k1lt82r8--wrapper">
																						<style style-id="id-8-346k1lt82r8">
																							[node-id="id-8-346k1lt82r8"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-8-346k1lt82r8"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-8-346k1lt82r8"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-8-346k1lt82r8"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									通用解決方案
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-8-346k1lt82r8",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-9-5kfgltlusq--wrapper">
																						<style style-id="id-9-5kfgltlusq">
																							[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"] {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-9-5kfgltlusq"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-9-5kfgltlusq"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-9-5kfgltlusq"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-9-5kfgltlusq"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-9-5kfgltlusq"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-9-5kfgltlusq"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E9%87%91%E4%BA%91-copy.html">
																											<span
																												class="cc-menu--item__title">
																												數金雲
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E4%BA%A4%E6%98%93%E7%BB%93%E7%AE%97.html">
																													<span
																														class="cc-menu--item__title">
																														交易結算
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E6%95%B0%E5%AD%97%E6%94%AF%E4%BB%98.html">
																													<span
																														class="cc-menu--item__title">
																														數字支付
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E9%87%91%E4%BA%91-copy/%E8%B7%A8%E5%A2%83%E6%B1%87%E6%AC%BE.html">
																													<span
																														class="cc-menu--item__title">
																														跨境匯款
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E7%A7%91%E4%BA%91.html">
																											<span
																												class="cc-menu--item__title">
																												數科雲
																											</span>
																										</a>
																										<i
																											class="fas fa-caret-down down-icon"></i>
																									</div>
																									<ul class="cc-menu--nav">
																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E8%BA%AB%E4%BB%BD%E8%AE%A4%E8%AF%81.html">
																													<span
																														class="cc-menu--item__title">
																														身份認證
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E6%95%B0%E6%8D%AE%E6%B2%BB%E7%90%86.html">
																													<span
																														class="cc-menu--item__title">
																														數據治理
																													</span>
																												</a>
																											</div>
																										</li>

																										<li class="cc-menu--item">
																											<div
																												class="cc-menu--item__link">
																												<a
																													target="_self"
																													href="%E6%95%B0%E7%A7%91%E4%BA%91/%E7%AE%97%E6%B3%95%E6%A8%A1%E5%9E%8B.html">
																													<span
																														class="cc-menu--item__title">
																														算法模型
																													</span>
																												</a>
																											</div>
																										</li>
																									</ul>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E6%95%B0%E9%87%91%E4%BA%91.html">
																											<span
																												class="cc-menu--item__title">
																												區塊鏈應用
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-9-5kfgltlusq",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-60-zz8x9v5o2r"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-60-zz8x9v5o2r",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-76-kzlmotmh3o--wrapper">
																		<div
																			node-id="id-76-kzlmotmh3o"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-76-kzlmotmh3o">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-76-kzlmotmh3o"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-76-kzlmotmh3o"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-76-kzlmotmh3o",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-75-mo3ppnz0vz"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-75-mo3ppnz0vz",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-57-s1prxoo2go"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-57-s1prxoo2go",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-81-lnvd0fgv2z--wrapper">
																						<style style-id="id-81-lnvd0fgv2z">
																							[node-id="id-81-lnvd0fgv2z"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-81-lnvd0fgv2z"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-81-lnvd0fgv2z"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-81-lnvd0fgv2z"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									AI Solution
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-81-lnvd0fgv2z",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-66-eizdndxdbx--wrapper">
																						<style style-id="id-66-eizdndxdbx">
																							[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"] {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-66-eizdndxdbx"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-66-eizdndxdbx"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-66-eizdndxdbx"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-66-eizdndxdbx"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-66-eizdndxdbx"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-66-eizdndxdbx"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/aigc.html">
																											<span
																												class="cc-menu--item__title">
																												垂類大模型
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/%E6%95%B0%E6%99%BA%E4%BA%BA.html">
																											<span
																												class="cc-menu--item__title">
																												數智人
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="ai%E5%BA%94%E7%94%A8/%E5%9E%82%E7%B1%BB%E5%A4%A7%E6%A8%A1%E5%9E%8B.html">
																											<span
																												class="cc-menu--item__title">
																												AI 應用
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-66-eizdndxdbx",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-19-v5tkhhkkj4"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-19-v5tkhhkkj4">
																		[node-id="id-19-v5tkhhkkj4"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-19-v5tkhhkkj4"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-19-v5tkhhkkj4",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-47-ndco63iww1--wrapper">
																		<style style-id="id-47-ndco63iww1">
																			[node-id="id-47-ndco63iww1"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-47-ndco63iww1"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-47-ndco63iww1"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-47-ndco63iww1"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-47-ndco63iww1",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-26-c1ds8m08s6--wrapper">
																		<style style-id="id-26-c1ds8m08s6">
																			[node-id="id-26-c1ds8m08s6"] {
																				padding-top: 40px;
																			}
																			[node-id="id-26-c1ds8m08s6"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-26-c1ds8m08s6"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-26-c1ds8m08s6"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-26-c1ds8m08s6"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					&ldquo;3+X&rdquo;業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="https://hqshuke.com/%e6%99%ba%e6%85%a7%e5%9f%8e%e5%b8%82"
																						>城市服務</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>&nbsp; &nbsp; |&nbsp; &nbsp;
																					<a
																						href="https://hqshuke.com/ai%e5%ba%94%e7%94%a8/%e5%9e%82%e7%b1%bb%e5%a4%a7%e6%a8%a1%e5%9e%8b"
																						>更多行業</a
																					>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-26-c1ds8m08s6",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-25-edi990jjid"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-25-edi990jjid">
																		[node-id="id-25-edi990jjid"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-25-edi990jjid",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-65-rg1fdfgvaf"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
																<style style-id="id-65-rg1fdfgvaf">
																	[node-id="id-65-rg1fdfgvaf"] {
																		background-color: rgba(229, 239, 251, 1);
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-65-rg1fdfgvaf",
																			options: {
																				"full-width": "full",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-88-mme92kv1ar"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-88-mme92kv1ar",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-31-ng2rngvclg"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-31-ng2rngvclg",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-51-x5jjg9mffn--wrapper">
																		<div
																			node-id="id-51-x5jjg9mffn"
																			node-type="block"
																			class="cc-block cc-slot--wrapper">
																			<style style-id="id-51-x5jjg9mffn">
																				@media only screen and (min-width: 768px) {
																					[node-id="id-51-x5jjg9mffn"] {
																						padding-top: 30px;
																					}
																				}
																				[node-id="id-51-x5jjg9mffn"] {
																					height: 430px;
																					overflow: hidden;
																					overflow-y: auto;
																				}
																			</style>
																			<script>
																				;(function () {
																					useComponent("block").default({
																						id: "id-51-x5jjg9mffn",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				node-id="id-78-w949t97y4v"
																				node-type="row"
																				class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																				<script>
																					;(function () {
																						useComponent("row").default({
																							id: "id-78-w949t97y4v",
																							options: {
																								"full-width": "default",
																								"adaption-height": "no",
																								"background-video": "",
																								"noheader-full-height": "no",
																								"auto-flex": [],
																								"auto-flex-enable": "no"
																							}
																						})
																					})()
																				</script>
																				<div
																					node-id="id-62-ocbx635o84"
																					node-type="column"
																					class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																					<script>
																						;(function () {
																							useComponent("column").default({
																								id: "id-62-ocbx635o84",
																								options: []
																							})
																						})()
																					</script>
																					<div
																						class="cc-element--wrapper id-64-vsnh2e3otr--wrapper">
																						<style style-id="id-64-vsnh2e3otr">
																							[node-id="id-64-vsnh2e3otr"]
																								.cc-textblock__body {
																								padding: 15px;
																							}
																							[node-id="id-64-vsnh2e3otr"].cc-textblock
																								.cc-textblock__body,
																							[node-id="id-64-vsnh2e3otr"].cc-textblock
																								.cc-textblock__body
																								* {
																								table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																								word-wrap: break-word; /*英文單字因自動換行*/
																								word-break: normal; /*正常避頭尾 */
																								text-align: justify; /*文字左右對齊*/
																								text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																							}
																						</style>
																						<div
																							node-id="id-64-vsnh2e3otr"
																							node-type="textblock"
																							class="cc-textblock">
																							<div
																								class="cc-textblock__body richtext">
																								<p
																									class="font-64361d5afc91249e3bd51e624b693b37">
																									成功案例
																								</p>
																							</div>
																						</div>

																						<script>
																							;(function () {
																								useComponent(
																									"textblock"
																								).default({
																									id: "id-64-vsnh2e3otr",
																									options: []
																								})
																							})()
																						</script>
																					</div>
																					<div
																						class="cc-element--wrapper id-96-qs92ow1w1r--wrapper">
																						<style style-id="id-96-qs92ow1w1r">
																							[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--vertical
																								.cc-menu--nav
																								.cc-menu--item {
																								box-sizing: border-box;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.line_box {
																								background: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.current
																								> .line_box {
																								width: 100%;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								li.menu {
																								color: #000000;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--nav
																								> .cc-menu--item
																								a {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal {
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal
																								> .cc-menu--nav
																								> .cc-menu--item {
																								height: 40px;
																								line-height: 40px;
																								padding: 0 20px;
																								margin: 0px 0px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																								> .cc-menu--nav
																								> .cc-menu--item {
																								margin: 0px 0;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								height: 40px;
																								line-height: 40px;
																								margin: 0px 0;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																								.cc-menu--item {
																								line-height: 40px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								> .cc-menu--nav
																								> .cc-menu--item {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item:hover {
																								background-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"] {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--item:hover {
																								border-bottom-color: rgba(
																									255,
																									255,
																									255,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item__link {
																								color: #333333;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item.active
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item {
																								background-color: rgba(
																									0,
																									181,
																									174,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover {
																								background-color: rgba(
																									0,
																									152,
																									146,
																									0
																								);
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item
																								.cc-menu--item__link {
																								color: #666666;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link {
																								color: #0064d8;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.block
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item.current
																								> .cc-menu--item__link,
																							[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: flex-start;
																								text-align: left;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--item
																								.cc-menu--item__link {
																								justify-content: space-between;
																							}
																							[node-id="id-96-qs92ow1w1r"]
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item__link
																								> i {
																								width: 30px;
																								transform: rotate(-90deg);
																								transform-origin: 50% 50%;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																								> .cc-menu--nav {
																								font-size: 16px;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																								.cc-menu--nav
																								.cc-menu--item
																								.cc-menu--item:hover
																								> .cc-menu--item__link {
																								color: #0064d8;
																							}
																							[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--auto__mini
																								.item-icon-active {
																								color: #3c3c3c !important;
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																									.cc-menu--expand__header {
																									display: block;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--auto
																									> .cc-menu--nav {
																									display: none;
																									opacity: 0; /*position: fixed;*/
																									position: relative;
																									z-index: 25;
																									width: 100%;
																									left: 0;
																									top: 50px;
																									height: calc(100% - 50px);
																									padding: 0 10px;
																									box-sizing: border-box;
																									overflow: hidden;
																									overflow-y: auto;
																								}
																							}
																							@media only screen and (max-width: 767px) {
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--nav
																									> .cc-menu--item
																									a {
																									font-size: 16px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--horizontal
																									> .cc-menu--nav
																									> .cc-menu--item {
																									height: 60px;
																									line-height: 60px;
																									padding: 0 20px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																									> .cc-menu--nav
																									> .cc-menu--item {
																									margin: 20px 0;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--vertical
																									.cc-menu--item {
																									line-height: 60px;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item {
																									background-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item:hover {
																									background-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__line
																									.cc-menu--item:hover {
																									border-bottom-color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item__link {
																									color: #3c3c3c;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item {
																									background-color: #00b5ae;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--style__default
																									.cc-menu--item
																									.cc-menu--item:hover,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.block,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item.current,
																								[node-id="id-96-qs92ow1w1r"].cc-menu--line-main
																									.cc-menu--item
																									.cc-menu--item:hover {
																									background-color: #009892;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.block
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item.current
																									> .cc-menu--item__link,
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item:hover
																									> .cc-menu--item__link {
																									color: #fff;
																								}
																								[node-id="id-96-qs92ow1w1r"].cc-menu.cc-menu--auto__mini
																									.cc-menu--trigger
																									i {
																									color: #000000;
																								}
																								[node-id="id-96-qs92ow1w1r"]
																									.cc-menu--item
																									.cc-menu--item
																									.cc-menu--item__link {
																									height: 43px;
																									line-height: 43px;
																									margin: 0px 0;
																								}
																							}
																							@media only screen and (min-width: 767px) {
																							}
																						</style>
																						<div
																							node-id="id-96-qs92ow1w1r"
																							node-type="menu"
																							class="cc-menu cc-menu--style__default cc-menu--vertical cc-menu--arrow-icon cc-menu--line-main">
																							<ul class="cc-menu--nav">
																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E6%99%BA%E6%85%A7%E7%9B%91%E7%AE%A1-1.html">
																											<span
																												class="cc-menu--item__title">
																												智慧文旅
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy.html">
																											<span
																												class="cc-menu--item__title">
																												城市服務
																											</span>
																										</a>
																									</div>
																								</li>

																								<li class="cc-menu--item">
																									<div
																										class="cc-menu--item__link">
																										<a
																											target="_self"
																											href="%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy-copy-2/%E7%81%BE%E5%AE%B3%E9%A2%84%E8%AD%A6-1-copy.html">
																											<span
																												class="cc-menu--item__title">
																												智慧生態
																											</span>
																										</a>
																									</div>
																								</li>
																							</ul>
																						</div>

																						<script>
																							;(function () {
																								useComponent("menu").default({
																									id: "id-96-qs92ow1w1r",
																									options: {
																										hover_show: "no",
																										show_cur_sub: "yes",
																										retain_hover: "none",
																										"line-style-obj":
																											"main_menu",
																										"line-style": "left",
																										mode: "vertical",
																										style: "default",
																										"menu-item-repulsion": "no"
																									}
																								})
																							})()
																						</script>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	node-id="id-54-rezgbka2gi"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-54-rezgbka2gi",
																				options: []
																			})
																		})()
																	</script>
																</div>
																<div
																	node-id="id-46-a3andl1s9d"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
																	<style style-id="id-46-a3andl1s9d">
																		[node-id="id-46-a3andl1s9d"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																		@media only screen and (min-width: 768px) {
																			[node-id="id-46-a3andl1s9d"] {
																				padding-top: 50px;
																				padding-bottom: 80px;
																				padding-left: 50px;
																			}
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-46-a3andl1s9d",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-33-xti5y10y1y--wrapper">
																		<style style-id="id-33-xti5y10y1y">
																			[node-id="id-33-xti5y10y1y"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-33-xti5y10y1y"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-33-xti5y10y1y"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-33-xti5y10y1y"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					瞭解<span style="color: #0064d8"
																						>環球數科</span
																					>
																				</p>
																				<p
																					class="font-64361d5afc91249e3bd51e624b693b37"
																					style="line-height: 1">
																					核心產品！
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-33-xti5y10y1y",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																	<div
																		class="cc-element--wrapper id-29-hk77z33i6e--wrapper">
																		<style style-id="id-29-hk77z33i6e">
																			[node-id="id-29-hk77z33i6e"] {
																				padding-top: 40px;
																			}
																			[node-id="id-29-hk77z33i6e"]
																				.cc-textblock__body {
																				padding: 0px;
																			}
																			[node-id="id-29-hk77z33i6e"].cc-textblock
																				.cc-textblock__body,
																			[node-id="id-29-hk77z33i6e"].cc-textblock
																				.cc-textblock__body
																				* {
																				table-layout: fixed; /*文字避首尾 -- 防止撐開*/
																				word-wrap: break-word; /*英文單字因自動換行*/
																				word-break: normal; /*正常避頭尾 */
																				text-align: justify; /*文字左右對齊*/
																				text-justify: inter-ideograph; /*用表意文本來排齊內容*/
																			}
																		</style>
																		<div
																			node-id="id-29-hk77z33i6e"
																			node-type="textblock"
																			class="cc-textblock">
																			<div class="cc-textblock__body richtext">
																				<p>
																					“3+X”業務佈局：
																					<a
																						href="%E6%99%BA%E6%85%A7%E6%96%87%E6%97%85.html"
																						>智慧文旅</a
																					>    |   
																					<a
																						href="%E6%99%BA%E6%85%A7%E5%9F%8E%E5%B8%82-1.html"
																						>智慧城市</a
																					>    |   
																					<a
																						href="%E6%99%BA%E6%85%A7%E7%94%9F%E6%80%81.html"
																						>智慧生態</a
																					>    |   
																					<a href="/通用解決方案">更多行業</a>
																				</p>
																			</div>
																		</div>

																		<script>
																			;(function () {
																				useComponent("textblock").default({
																					id: "id-29-hk77z33i6e",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
																<div
																	node-id="id-85-c0j06hhhae"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-4 cc-col-xl-4 cc-col-lg3-4 cc-col-lg2-4 cc-col-lg-4 cc-col-md-4 cc-col-sm-4 cc-col-xs-24">
																	<style style-id="id-85-c0j06hhhae">
																		[node-id="id-85-c0j06hhhae"] {
																			background-color: rgba(255, 255, 255, 1);
																		}
																	</style>
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-85-c0j06hhhae",
																				options: []
																			})
																		})()
																	</script>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-15-96ie10ai9h"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-15-96ie10ai9h",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-14-248l29jedn8"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-14-248l29jedn8",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-13-30e961u44h8--wrapper">
																		<div
																			node-id="id-13-30e961u44h8"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-13-30e961u44h8",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="
																height: 430px;
																background: rgba(250, 250, 250, 1);
															">
															<div
																node-id="id-18-urn9q83d06g"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-18-urn9q83d06g",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-17-akos8nnpsp"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-17-akos8nnpsp",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-16-6fkulllotpo--wrapper">
																		<div
																			node-id="id-16-6fkulllotpo"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-16-6fkulllotpo",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
														<div
															class="cc-slot--wrapper cc-menudropdown--dropdown cc-slot--id-60-nkt0mtoceo cc-menudropdown--width__full"
															style="height: 300px; background: #fff">
															<div
																node-id="id-21-gbmf0a7qui"
																node-type="row"
																class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																<script>
																	;(function () {
																		useComponent("row").default({
																			id: "id-21-gbmf0a7qui",
																			options: {
																				"full-width": "default",
																				"adaption-height": "no",
																				"background-video": "",
																				"noheader-full-height": "no",
																				"auto-flex": [],
																				"auto-flex-enable": "no"
																			}
																		})
																	})()
																</script>
																<div
																	node-id="id-20-rlok5g7u0l"
																	node-type="column"
																	class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																	<script>
																		;(function () {
																			useComponent("column").default({
																				id: "id-20-rlok5g7u0l",
																				options: []
																			})
																		})()
																	</script>
																	<div
																		class="cc-element--wrapper id-19-9fg87k3v2ro--wrapper">
																		<div
																			node-id="id-19-9fg87k3v2ro"
																			node-type="placeholder"
																			class="cc-placeholder"
																			style="height: 50px"></div>

																		<script>
																			;(function () {
																				useComponent("placeholder").default({
																					id: "id-19-9fg87k3v2ro",
																					options: []
																				})
																			})()
																		</script>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="Page-header--widgets"></div>
							</div>
						</div>
					</div>
					<div class="Page-header--main__placeholder"></div>
				</div>
				<div class="Page-body">
					<div class="Page-sidebar sidebar-left"></div>
					<div class="Page-content">
						<div
							node-id="id-10-9bs857pt6vo"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-10-9bs857pt6vo">
								[node-id="id-10-9bs857pt6vo"] {
									background-image: url(uploads/sites/1012/2022/11/fb69a0b931bec71a8974c0cd9f3a10be.jpg);
									background-size: cover;
									background-position: right bottom;
									background-repeat: repeat;
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-10-9bs857pt6vo",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-11-r7htj5ul6m"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-11-r7htj5ul6m",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-12-ln308vdq5m8--wrapper">
									<div
										node-id="id-12-ln308vdq5m8"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<style style-id="id-12-ln308vdq5m8">
											@media only screen and (max-width: 767px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 50px;
													padding-bottom: 50px;
												}
											}
											@media only screen and (min-width: 768px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 120px;
													padding-bottom: 120px;
												}
											}
											@media only screen and (min-width: 1200px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 150px;
													padding-bottom: 150px;
												}
											}
											@media only screen and (min-width: 1360px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 180px;
													padding-bottom: 180px;
												}
											}
											@media only screen and (min-width: 1600px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 220px;
													padding-bottom: 220px;
												}
											}
											@media only screen and (min-width: 1920px) {
												[node-id="id-12-ln308vdq5m8"] {
													padding-top: 220px;
													padding-bottom: 220px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-12-ln308vdq5m8",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-13-k1kpva4ou3o"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-13-k1kpva4ou3o",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-14-cppslltolvo"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-14-cppslltolvo",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-15-t5efknkdj1g--wrapper">
													<style style-id="id-15-t5efknkdj1g">
														[node-id="id-15-t5efknkdj1g"] .cc-textblock__body {
															padding: 20px;
														}
														[node-id="id-15-t5efknkdj1g"].cc-textblock
															.cc-textblock__body,
														[node-id="id-15-t5efknkdj1g"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-15-t5efknkdj1g"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-1ea24a2ccc91fca49f6e4455d2a5f757"
																style="line-height: 1">
																<strong
																	><span style="color: #ffffff"
																		>Solution</span
																	></strong
																>
															</p>
<!--															<p-->
<!--																class="font-64361d5afc91249e3bd51e624b693b37"-->
<!--																style="line-height: 1">-->
<!--																<span style="color: #ffffff"-->
<!--																	><span>SOLUTION</span></span-->
<!--																>-->
<!--															</p>-->
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-15-t5efknkdj1g",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-16-oul7l48a3dg"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-16-oul7l48a3dg">
								[node-id="id-16-oul7l48a3dg"] {
									background-color: rgba(238, 238, 238, 1);
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-16-oul7l48a3dg",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-17-4797g7i9rso"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-17-4797g7i9rso",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-18-piqr4lcrgv8--wrapper">
									<div
										node-id="id-18-piqr4lcrgv8"
										node-type="block"
										class="cc-block cc-slot--wrapper">
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-18-piqr4lcrgv8",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-19-udsh8f1ta7g"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-19-udsh8f1ta7g",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-20-9n81pp1p9ag"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-16 cc-col-xl-16 cc-col-lg3-16 cc-col-lg2-16 cc-col-lg-16 cc-col-md-16 cc-col-sm-16 cc-col-xs-24">
												<style style-id="id-20-9n81pp1p9ag">
													@media only screen and (max-width: 767px) {
														[node-id="id-20-9n81pp1p9ag"] {
															border-bottom-color: #cccccc;
															border-bottom-width: 1px;
															border-style: solid;
														}
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-20-9n81pp1p9ag",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-21-ihuvsld9ae8--wrapper">
													<style style-id="id-21-ihuvsld9ae8">
														[node-id="id-21-ihuvsld9ae8"].cc-menu.cc-menu--vertical
															.cc-menu--nav
															.cc-menu--item {
															box-sizing: border-box;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.line_box {
															background: rgba(16, 100, 169, 1);
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.cc-menu--item.current
															> .line_box {
															width: 100%;
														}
														[node-id="id-21-ihuvsld9ae8"] li.menu {
															color: #000000;
														}
														[node-id="id-21-ihuvsld9ae8"] .icon-active {
															color: #3c3c3c !important;
														}
														[node-id="id-21-ihuvsld9ae8"] .item-icon-active {
															color: #3c3c3c !important;
														}
														[node-id="id-21-ihuvsld9ae8"] .cc-menu--item a {
															font-size: 16px;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--nav
															> .cc-menu--item
															a {
															font-size: 16px;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--horizontal {
															text-align: left;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--horizontal
															> .cc-menu--nav
															> .cc-menu--item {
															height: 80px;
															line-height: 80px;
															padding: 0 20px;
															margin: 0px 0px;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--vertical
															> .cc-menu--nav
															> .cc-menu--item {
															margin: 0px 0;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item
															.cc-menu--item__link {
															height: 43px;
															line-height: 43px;
															margin: 0px 0;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--vertical
															.cc-menu--item {
															line-height: 80px;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															> .cc-menu--nav
															> .cc-menu--item {
															background-color: rgba(255, 255, 255, 0);
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item.block,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item.current,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item:hover {
															background-color: rgba(16, 100, 169, 1);
														}
														[node-id="id-21-ihuvsld9ae8"] {
															font-size: 16px;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.cc-menu--item.block,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.cc-menu--item.current,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.cc-menu--item:hover {
															border-bottom-color: rgba(16, 100, 169, 1);
														}
														[node-id="id-21-ihuvsld9ae8"] .cc-menu--item__link {
															color: #333333;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item.block
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item.current
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item:hover
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item.active
															> .cc-menu--item__link {
															color: #1064a9;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item {
															background-color: #00b5ae;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item.block,
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item.current,
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item:hover,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item.block,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item.current,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item:hover,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item.block,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item.current,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item:hover {
															background-color: #009892;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item
															.cc-menu--item__link {
															color: #fff;
															text-align: left;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item.current
															> .cc-menu--item__link {
															color: #fff;
															text-align: left;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item.block
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item.current
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
															.cc-menu--item
															.cc-menu--item:hover
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item.block
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item.current
															> .cc-menu--item__link,
														[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
															.cc-menu--item
															.cc-menu--item:hover
															> .cc-menu--item__link {
															color: #fff;
														}
														[node-id="id-21-ihuvsld9ae8"]
															.cc-menu--item
															.cc-menu--item__link {
															justify-content: flex-start;
															text-align: left;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--auto
															> .cc-menu--nav {
															font-size: 16px;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
															.cc-menu--nav
															.cc-menu--item
															.cc-menu--item:hover
															> .cc-menu--item__link {
															color: #fff;
														}
														[node-id="id-21-ihuvsld9ae8"].cc-menu.cc-menu--auto__mini
															.item-icon-active {
															color: #3c3c3c !important;
														}
														@media only screen and (max-width: 767px) {
															[node-id="id-21-ihuvsld9ae8"].cc-menu--auto
																.cc-menu--expand__header {
																display: block;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--auto
																> .cc-menu--nav {
																display: none;
																opacity: 0; /*position: fixed;*/
																position: relative;
																z-index: 25;
																width: 100%;
																left: 0;
																top: 50px;
																height: calc(100% - 50px);
																padding: 0 10px;
																box-sizing: border-box;
																overflow: hidden;
																overflow-y: auto;
															}
														}
														@media only screen and (max-width: 767px) {
															[node-id="id-21-ihuvsld9ae8"] .cc-menu--item a {
																font-size: 16px;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--nav
																> .cc-menu--item
																a {
																font-size: 16px;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--horizontal
																> .cc-menu--nav
																> .cc-menu--item {
																height: 60px;
																line-height: 60px;
																padding: 0 20px;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--vertical
																> .cc-menu--nav
																> .cc-menu--item {
																margin: 20px 0;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--vertical
																.cc-menu--item {
																line-height: 60px;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item {
																background-color: #fff;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item.block,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item.current,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item:hover {
																background-color: rgba(207, 14, 15, 0);
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
																.cc-menu--item.block,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
																.cc-menu--item.current,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__line
																.cc-menu--item:hover {
																border-bottom-color: rgba(207, 14, 15, 0);
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item__link {
																color: #333333;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item.block
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item.current
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item:hover
																> .cc-menu--item__link {
																color: #1064a9;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item {
																background-color: #00b5ae;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item.block
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item.current
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item:hover
																> .cc-menu--item__link {
																color: #1064a9;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item {
																background-color: #00b5ae;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item.block,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item.current,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item:hover,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item
																.cc-menu--item.block,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item
																.cc-menu--item.current,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--style__default
																.cc-menu--item
																.cc-menu--item:hover,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
																.cc-menu--item
																.cc-menu--item.block,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
																.cc-menu--item
																.cc-menu--item.current,
															[node-id="id-21-ihuvsld9ae8"].cc-menu--line-main
																.cc-menu--item
																.cc-menu--item:hover {
																background-color: #009892;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item
																.cc-menu--item__link {
																color: #fff;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--trigger
																i {
																color: #000000;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item.block
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item.current
																> .cc-menu--item__link,
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item:hover
																> .cc-menu--item__link {
																color: #fff;
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu.cc-menu--auto__mini
																.cc-menu--trigger
																i {
																color: #000000;
															}
															[node-id="id-21-ihuvsld9ae8"]
																.cc-menu--item
																.cc-menu--item
																.cc-menu--item__link {
																height: 43px;
																line-height: 43px;
																margin: 0px 0;
															}
														}
														@media screen and (min-width: 767px) {
															[node-id="id-21-ihuvsld9ae8"].cc-menu
																> .cc-menu--nav
																> .cc-menu--item::after {
																content: "";
																position: absolute;
																right: 0;
																top: 0;
																height: 15px;
																bottom: 0;
																margin: auto;
																width: 1px;
																background-color: rgba(204, 204, 204, 1);
															}
															[node-id="id-21-ihuvsld9ae8"].cc-menu
																> .cc-menu--nav
																> :nth-last-of-type(1)::after {
																content: "";
																position: absolute;
																right: 0;
																top: 10%;
																height: 0px;
																bottom: 10%;
																width: 0px;
															}
														}
														@media only screen and (min-width: 767px) {
														}
													</style>
													<div
														node-id="id-21-ihuvsld9ae8"
														node-type="menu"
														class="cc-menu cc-menu--style__line cc-menu--horizontal cc-menu--nowrap cc-menu--line-main">
														<ul class="cc-menu--nav">
															<li class="cc-menu--item">
																<div class="cc-menu--item__link">
																	<a
																		target="_self"
																		href="./smart-culturetourism.html#Industry Pain Points">
																		<span class="cc-menu--item__title">
																			Industry Pain Points
																		</span>
																	</a>
																</div>
															</li>

															<li class="cc-menu--item">
																<div class="cc-menu--item__link">
																	<a
																		target="_self"
																		href="./smart-culturetourism.html#Development Direction">
																		<span class="cc-menu--item__title">
																			Development Direction
																		</span>
																	</a>
																</div>
															</li>

															<li class="cc-menu--item">
																<div class="cc-menu--item__link">
																	<a
																		target="_self"
																		href="./smart-culturetourism.html#Program Highlights">
																		<span class="cc-menu--item__title">
																			Program Highlights
																		</span>
																	</a>
																</div>
															</li>
														</ul>
													</div>

													<script>
														;(function () {
															useComponent("menu").default({
																id: "id-21-ihuvsld9ae8",
																options: {
																	hover_show: "no",
																	show_cur_sub: "no",
																	retain_hover: "none",
																	"line-style-obj": "main_menu",
																	"line-style": "left",
																	mode: "horizontal",
																	style: "line",
																	"menu-item-repulsion": "no"
																}
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-22-39m2r1s38u"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__middle cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-22-39m2r1s38u",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-15-nc0ud06imi--wrapper">
													<style style-id="id-15-nc0ud06imi">
														[node-id="id-15-nc0ud06imi"] {
															padding-right: 20px;
														}
														[node-id="id-15-nc0ud06imi"].cc-breadcrumb
															li:nth-child(n + 2)::before {
															content: ">";
														}
														[node-id="id-15-nc0ud06imi"].cc-breadcrumb {
															color: rgba(102, 102, 102, 1);
															font-size: 16px;
														}
														[node-id="id-15-nc0ud06imi"].cc-breadcrumb a:hover {
															color: #000;
														}
													</style>
													
													<script>
														;(function () {
															useComponent("breadcrumb").default({
																id: "id-15-nc0ud06imi",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-12-jv82ntv3si"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-12-jv82ntv3si",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-13-2csj303dea"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-0 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-0 cc-col-md-0 cc-col-sm-0 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-13-2csj303dea",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-23-m3jeovfeu5--wrapper">
													<style style-id="id-23-m3jeovfeu5">
														[node-id="id-23-m3jeovfeu5"] {
															padding-right: 20px;
														}
														@media only screen and (max-width: 767px) {
															[node-id="id-23-m3jeovfeu5"] {
																padding-top: 20px;
																padding-bottom: 20px;
																padding-left: 20px;
															}
														}
														[node-id="id-23-m3jeovfeu5"].cc-breadcrumb
															li:nth-child(n + 2)::before {
															content: ">";
														}
														[node-id="id-23-m3jeovfeu5"].cc-breadcrumb {
															color: rgba(102, 102, 102, 1);
															font-size: 14px;
														}
														[node-id="id-23-m3jeovfeu5"].cc-breadcrumb a:hover {
															color: #000;
														}
													</style>
													
													<script>
														;(function () {
															useComponent("breadcrumb").default({
																id: "id-23-m3jeovfeu5",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-17-nk7lhrokato"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__full">
							<style style-id="id-17-nk7lhrokato">
								[node-id="id-17-nk7lhrokato"] {
									background-image: url(uploads/sites/264/2022/08/32d3119d8b5af6f967f0d45c9ecf95f3.jpg);
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-17-nk7lhrokato",
										options: {
											"full-width": "full",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-18-nijdncdk2jg"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-18-nijdncdk2jg",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-19-4ug90j0i4v8--wrapper">
									<div
										node-id="id-19-4ug90j0i4v8"
										node-type="block"
										class="cc-block cc-slot--wrapper"
										id="Industry Pain Points">
										<style style-id="id-19-4ug90j0i4v8">
											@media only screen and (max-width: 767px) {
												[node-id="id-19-4ug90j0i4v8"] {
													padding-right: 30px;
													padding-left: 30px;
													padding-top: 30px;
													padding-bottom: 30px;
												}
											}
											@media only screen and (min-width: 768px) {
												[node-id="id-19-4ug90j0i4v8"] {
													padding-right: 30px;
													padding-left: 30px;
													padding-top: 60px;
													padding-bottom: 60px;
												}
											}
											@media only screen and (min-width: 1360px) {
												[node-id="id-19-4ug90j0i4v8"] {
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 1600px) {
												[node-id="id-19-4ug90j0i4v8"] {
													padding-right: 1px;
													padding-left: 1px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-19-4ug90j0i4v8",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-29-8v4v42c4tjo"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-29-8v4v42c4tjo",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-30-fvso6s1331o"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-30-fvso6s1331o",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-32-cjbkl3jn86g--wrapper">
													<style style-id="id-32-cjbkl3jn86g">
														@media only screen and (max-width: 767px) {
															[node-id="id-32-cjbkl3jn86g"] {
																padding-bottom: 30px;
															}
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-32-cjbkl3jn86g"] {
																padding-bottom: 30px;
															}
														}
														[node-id="id-32-cjbkl3jn86g"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-32-cjbkl3jn86g"].cc-textblock
															.cc-textblock__body,
														[node-id="id-32-cjbkl3jn86g"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-32-cjbkl3jn86g"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-48058e19f5c604983923df72ff2dd684"
																style="text-align: center">
																Industry Pain Points
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-32-cjbkl3jn86g",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-32-gm5t065s14g"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-32-gm5t065s14g",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-33-3hsvuv68mkg"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-6 cc-col-xl-6 cc-col-lg3-6 cc-col-lg2-6 cc-col-lg-6 cc-col-md-6 cc-col-sm-6 cc-col-xs-24 hvr-float">
												<style style-id="id-33-3hsvuv68mkg">
													@media only screen and (min-width: 768px) {
														[node-id="id-33-3hsvuv68mkg"] {
															padding-right: 20px;
														}
													}
													[node-id="id-33-3hsvuv68mkg"] {
														transition-duration: 0.3s;
													}
													[node-id="id-33-3hsvuv68mkg"]:hover {
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-33-3hsvuv68mkg",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-26-dssui2jtr88--wrapper">
													<style style-id="id-26-dssui2jtr88">
														[node-id="id-26-dssui2jtr88"] {
															background-color: rgba(255, 255, 255, 1);
															border-radius: 10px;
															background-position: center bottom;
															background-repeat: no-repeat;
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-26-dssui2jtr88"] {
																padding-right: 30px;
																padding-left: 30px;
															}
														}
														[node-id="id-26-dssui2jtr88"] .cc-imagetext--image {
															width: 36%;
														}
														[node-id="id-26-dssui2jtr88"] .cc-imagetext--text {
															width: calc(100% - 36%);
														}
														[node-id="id-26-dssui2jtr88"]
															.cc-imagetext--body
															.cc-imagetext--image {
															text-align: center;
														}
													</style>
													<div
														node-id="id-26-dssui2jtr88"
														node-type="imagetext"
														class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top custom-al">
														<div class="cc-imagetext--body">
															<div class="cc-imagetext--image">
<!--																<a target="_self" href="#"-->
																<a target="_self"
																	><img
																		src="uploads/sites/1012/2022/12/1dd72ae924365dd33f938a045f1afd99.png"
																/></a>
															</div>

															<div class="cc-imagetext--text richtext">
																<p
																	class="font-64361d5afc91249e3bd51e624b693b37"
																	style="text-align: center">
																	<strong>Regulation</strong>
																</p>
																<p style="text-align: left">
																	Under the traditional tourism industry regulatory model, regulatory authorities lack effective channels for obtaining industry data, industry data lags behind, and lack dynamic supervision and management capabilities.
																</p>
																<p style="text-align: left">
																	At the same time, due to the lack of data support for decision-making, the operational capabilities for value mining, utilization and development of industrial resources are also weak.
																</p>
																<p style="text-align: left; line-height: 1">
																	 
																</p>
																<p style="text-align: left; line-height: 0.9">
																	 
																</p>
															</div>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("imagetext").default({
																id: "id-26-dssui2jtr88",
																options: { href_shared: "no" }
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-35-denrufpmnk"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-6 cc-col-xl-6 cc-col-lg3-6 cc-col-lg2-6 cc-col-lg-6 cc-col-md-6 cc-col-sm-6 cc-col-xs-24 hvr-float">
												<style style-id="id-35-denrufpmnk">
													@media only screen and (max-width: 767px) {
														[node-id="id-35-denrufpmnk"] {
															margin-top: 30px;
														}
													}
													@media only screen and (min-width: 768px) {
														[node-id="id-35-denrufpmnk"] {
															padding-right: 10px;
															padding-left: 10px;
														}
													}
													[node-id="id-35-denrufpmnk"] {
														transition-duration: 0.3s;
													}
													[node-id="id-35-denrufpmnk"]:hover {
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-35-denrufpmnk",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-34-t16m9oru1ro--wrapper">
													<style style-id="id-34-t16m9oru1ro">
														[node-id="id-34-t16m9oru1ro"] {
															background-color: rgba(255, 255, 255, 1);
															border-radius: 10px;
															background-position: center bottom;
															background-repeat: no-repeat;
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-34-t16m9oru1ro"] {
																padding-right: 30px;
																padding-left: 30px;
															}
														}
														[node-id="id-34-t16m9oru1ro"] .cc-imagetext--image {
															width: 36%;
														}
														[node-id="id-34-t16m9oru1ro"] .cc-imagetext--text {
															width: calc(100% - 36%);
														}
														[node-id="id-34-t16m9oru1ro"]
															.cc-imagetext--body
															.cc-imagetext--image {
															text-align: center;
														}
													</style>
													<div
														node-id="id-34-t16m9oru1ro"
														node-type="imagetext"
														class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top custom-al">
														<div class="cc-imagetext--body">
															<div class="cc-imagetext--image">
																<a target="_self"
																	><img
																		src="uploads/sites/1012/2022/12/9ca665f8d1de60c62172efdc1a59b20d.png"
																/></a>
															</div>

															<div class="cc-imagetext--text richtext">
																<p
																	class="font-64361d5afc91249e3bd51e624b693b37"
																	style="text-align: center">
																	<strong>Scenic Spots</strong>
																</p>
																<p style="text-align: left">
																	Under the traditional scenic spot model, scenic spots face traditional ticket checking methods, unsystematic tour guide guidance, and inadequate safety supervision.
																</p>
																<p style="text-align: left">
																	Channel management efficiency is low,dependence is serious, and operating costs are high.
																</p>
																<p style="text-align: left">
																	The payment methods are not perfect, the transaction settlement with channels is complicated, and the fund management efficiency is low.
																</p>
																<p style="text-align: left"> </p>
															</div>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("imagetext").default({
																id: "id-34-t16m9oru1ro",
																options: { href_shared: "no" }
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-37-ra94bdv10vg"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-6 cc-col-xl-6 cc-col-lg3-6 cc-col-lg2-6 cc-col-lg-6 cc-col-md-6 cc-col-sm-6 cc-col-xs-24 hvr-float">
												<style style-id="id-37-ra94bdv10vg">
													@media only screen and (max-width: 767px) {
														[node-id="id-37-ra94bdv10vg"] {
															margin-top: 30px;
														}
													}
													@media only screen and (min-width: 768px) {
														[node-id="id-37-ra94bdv10vg"] {
															padding-right: 10px;
															padding-left: 10px;
														}
													}
													[node-id="id-37-ra94bdv10vg"] {
														transition-duration: 0.3s;
													}
													[node-id="id-37-ra94bdv10vg"]:hover {
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-37-ra94bdv10vg",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-24-371grdbhabo--wrapper">
													<style style-id="id-24-371grdbhabo">
														[node-id="id-24-371grdbhabo"] {
															background-color: rgba(255, 255, 255, 1);
															border-radius: 10px;
															background-position: center bottom;
															background-repeat: no-repeat;
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-24-371grdbhabo"] {
																padding-right: 30px;
																padding-left: 30px;
															}
														}
														[node-id="id-24-371grdbhabo"] .cc-imagetext--image {
															width: 36%;
														}
														[node-id="id-24-371grdbhabo"] .cc-imagetext--text {
															width: calc(100% - 36%);
														}
														[node-id="id-24-371grdbhabo"]
															.cc-imagetext--body
															.cc-imagetext--image {
															text-align: center;
														}
													</style>
													<div
														node-id="id-24-371grdbhabo"
														node-type="imagetext"
														class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top custom-al">
														<div class="cc-imagetext--body">
															<div class="cc-imagetext--image">
																<a target="_self"
																	><img
																		src="uploads/sites/1012/2022/12/407265a020586898f04ef2ca2b724faf.png"
																/></a>
															</div>

															<div class="cc-imagetext--text richtext">
																<p
																	class="font-64361d5afc91249e3bd51e624b693b37"
																	style="text-align: center">
																	<strong>Merchants</strong>
																</p>
																<p style="text-align: left">
																	There are certain deficiencies in the richness and personalization of the products provided to tourists.
																</p>
																<p style="text-align: left">
																	The transaction settlement with factor merchants is relatively scattered and inefficient in terms of capital flow, cash flow and data flow management.
																</p>
																<p style="text-align: left">
																	The information and data of each department are separated, and the marketing capabilities are relatively scattered.
																</p>
																<p style="text-align: left"> </p>
															</div>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("imagetext").default({
																id: "id-24-371grdbhabo",
																options: { href_shared: "no" }
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-18-9l7j7b0vphg"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-6 cc-col-xl-6 cc-col-lg3-6 cc-col-lg2-6 cc-col-lg-6 cc-col-md-6 cc-col-sm-6 cc-col-xs-24 hvr-float">
												<style style-id="id-18-9l7j7b0vphg">
													@media only screen and (max-width: 767px) {
														[node-id="id-18-9l7j7b0vphg"] {
															padding-top: 30px;
														}
													}
													@media only screen and (min-width: 768px) {
														[node-id="id-18-9l7j7b0vphg"] {
															padding-left: 20px;
														}
													}
													[node-id="id-18-9l7j7b0vphg"] {
														transition-duration: 0.3s;
													}
													[node-id="id-18-9l7j7b0vphg"]:hover {
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-18-9l7j7b0vphg",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-25-uf27p2sjis--wrapper">
													<style style-id="id-25-uf27p2sjis">
														[node-id="id-25-uf27p2sjis"] {
															background-color: rgba(255, 255, 255, 1);
															border-radius: 10px;
															background-position: center bottom;
															background-repeat: no-repeat;
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-25-uf27p2sjis"] {
																padding-right: 30px;
																padding-left: 30px;
															}
														}
														[node-id="id-25-uf27p2sjis"] .cc-imagetext--image {
															width: 36%;
														}
														[node-id="id-25-uf27p2sjis"] .cc-imagetext--text {
															width: calc(100% - 36%);
														}
														[node-id="id-25-uf27p2sjis"]
															.cc-imagetext--body
															.cc-imagetext--image {
															text-align: center;
														}
													</style>
													<div
														node-id="id-25-uf27p2sjis"
														node-type="imagetext"
														class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top custom-al">
														<div class="cc-imagetext--body">
															<div class="cc-imagetext--image">
																<a target="_self"
																	><img
																		src="uploads/sites/1012/2022/12/b641648dd2e242c69724cf9f2ef26a5d.png"
																/></a>
															</div>

															<div class="cc-imagetext--text richtext">
																<p
																	class="font-64361d5afc91249e3bd51e624b693b37"
																	style="text-align: center">
																	<strong>Consumer</strong>
																</p>
																<p style="text-align: left">
																	Based on the tourism information provided by third-party platforms such as OTA, it is still difficult for consumers to obtain personalized and non-standard tourism resources at the destination, and they are unable to obtain comprehensive and in-depth destination tourism information.
																</p>
																<p style="text-align: left">
																	The tourism experience has not been fundamentally improved.
																</p>
																<p style="text-align: left"> </p>
																<p style="text-align: center"> </p>
															</div>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("imagetext").default({
																id: "id-25-uf27p2sjis",
																options: { href_shared: "no" }
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-35-e3js2tu125"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-35-e3js2tu125">
								[node-id="id-35-e3js2tu125"] {
									background-position: center center;
									background-repeat: repeat;
									background-image: url(uploads/sites/1012/2022/11/8162fe750695744e66454981f64cd904.jpg);
									background-size: cover;
									background-attachment: fixed;
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-35-e3js2tu125",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-36-mu2mc8unn8o"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-36-mu2mc8unn8o",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-37-1q9ov4v2iqo--wrapper">
									<div
										node-id="id-37-1q9ov4v2iqo"
										node-type="block"
										class="cc-block cc-slot--wrapper"
										id="Development Direction">
										<style style-id="id-37-1q9ov4v2iqo">
											@media only screen and (max-width: 767px) {
												[node-id="id-37-1q9ov4v2iqo"] {
													padding-top: 30px;
													padding-bottom: 30px;
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 768px) {
												[node-id="id-37-1q9ov4v2iqo"] {
													padding-top: 60px;
													padding-bottom: 60px;
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 1360px) {
												[node-id="id-37-1q9ov4v2iqo"] {
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 1600px) {
												[node-id="id-37-1q9ov4v2iqo"] {
													padding-right: 1px;
													padding-left: 1px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-37-1q9ov4v2iqo",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-38-sos1pfitac"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-38-sos1pfitac",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-39-qk9tcurgc9o"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-39-qk9tcurgc9o",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-40-1102gtmnuog--wrapper">
													<style style-id="id-40-1102gtmnuog">
														@media only screen and (min-width: 768px) {
															[node-id="id-40-1102gtmnuog"] {
																padding-bottom: 10px;
															}
														}
														[node-id="id-40-1102gtmnuog"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-40-1102gtmnuog"].cc-textblock
															.cc-textblock__body,
														[node-id="id-40-1102gtmnuog"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-40-1102gtmnuog"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-48058e19f5c604983923df72ff2dd684"
																style="text-align: center">
																<span style="color: #333333">Development Direction</span>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-40-1102gtmnuog",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-43-r7r508ab4io"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-43-r7r508ab4io",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-44-3crccclabcg"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-44-3crccclabcg",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-71-3fs0oosqr3g--wrapper">
													<style style-id="id-71-3fs0oosqr3g">
														[node-id="id-71-3fs0oosqr3g"] .cc-textblock__body {
															padding: 20px;
														}
														[node-id="id-71-3fs0oosqr3g"].cc-textblock
															.cc-textblock__body,
														[node-id="id-71-3fs0oosqr3g"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-71-3fs0oosqr3g"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p style="text-align: left">
																<span style="color: #666666"
																	>From the perspective of the tourism industry chain, scenic spots are no longer independent entities. The "three trends" of the cultural and tourism industry are strengthening - scenic spots are becoming destinations, supervision is becoming more precise, and operations are becoming integrated. More innovative models of the cultural and tourism industry will be continuously stimulated. Whether it is service, operation or management, everything is constantly evolving towards standardization.</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-71-3fs0oosqr3g",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-48-803rnca0k2o"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<style style-id="id-48-803rnca0k2o">
												@media only screen and (min-width: 768px) {
													[node-id="id-48-803rnca0k2o"] {
														padding-top: 10px;
													}
												}
											</style>
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-48-803rnca0k2o",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-49-n6qqovqfvu"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-7 cc-col-xl-7 cc-col-lg3-7 cc-col-lg2-7 cc-col-lg-7 cc-col-md-7 cc-col-sm-7 cc-col-xs-24">
												<style style-id="id-49-n6qqovqfvu">
													[node-id="id-49-n6qqovqfvu"] {
														background-position: right center;
														background-repeat: no-repeat;
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-49-n6qqovqfvu",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-54-juiipv4dtkg--wrapper">
													<style style-id="id-54-juiipv4dtkg">
														[node-id="id-54-juiipv4dtkg"] {
															background-color: rgba(0, 100, 216, 1);
															border-radius: 10px;
														}
														[node-id="id-54-juiipv4dtkg"] .cc-textblock__body {
															padding: 30px;
														}
														[node-id="id-54-juiipv4dtkg"].cc-textblock
															.cc-textblock__body,
														[node-id="id-54-juiipv4dtkg"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-54-juiipv4dtkg"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-64361d5afc91249e3bd51e624b693b37"
																style="text-align: center">
																<span style="color: #ffffff">Scenic spot destination</span>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-54-juiipv4dtkg",
																options: []
															})
														})()
													</script>
												</div>
												<div
													class="cc-element--wrapper id-62-7qqndm9mst--wrapper">
													<style style-id="id-62-7qqndm9mst">
														@media only screen and (max-width: 767px) {
															[node-id="id-62-7qqndm9mst"] {
																padding-bottom: 30px;
															}
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-62-7qqndm9mst"] {
																padding-top: 30px;
															}
														}
														[node-id="id-62-7qqndm9mst"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-62-7qqndm9mst"].cc-textblock
															.cc-textblock__body,
														[node-id="id-62-7qqndm9mst"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-62-7qqndm9mst"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #666666"
																	>Scenic spots are gradually moving away from the ticket-based business model and turning to a more diversified product packaging business model to achieve revenue at multiple business levels.</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-62-7qqndm9mst",
																options: []
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-51-s3nq8fugi58"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-1 cc-col-xl-1 cc-col-lg3-1 cc-col-lg2-1 cc-col-lg-1 cc-col-md-1 cc-col-sm-1 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-51-s3nq8fugi58",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-59-ftsaopas23--wrapper">
													<style style-id="id-59-ftsaopas23">
														@media only screen and (min-width: 768px) {
															[node-id="id-59-ftsaopas23"] {
																transform: translateX(0px) translateY(25px);
															}
														}
													</style>
													<div
														node-id="id-59-ftsaopas23"
														node-type="picture"
														class="cc-picture cc-picture--align__center">
														<img
															class="cc-picture--img"
															src="uploads/sites/1012/2022/11/198a0cc056df2660fb4e9b32ff4d2a81.png" />
													</div>

													<script>
														;(function () {
															useComponent("picture").default({
																id: "id-59-ftsaopas23",
																options: { handler: [] }
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-52-b88nbu4pfd8"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
												<style style-id="id-52-b88nbu4pfd8">
													@media only screen and (min-width: 768px) {
														[node-id="id-52-b88nbu4pfd8"] {
															padding-right: 20px;
															padding-left: 20px;
														}
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-52-b88nbu4pfd8",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-57-d650unu65bg--wrapper">
													<style style-id="id-57-d650unu65bg">
														[node-id="id-57-d650unu65bg"] {
															background-color: rgba(0, 100, 216, 1);
															border-radius: 10px;
														}
														[node-id="id-57-d650unu65bg"] .cc-textblock__body {
															padding: 30px;
														}
														[node-id="id-57-d650unu65bg"].cc-textblock
															.cc-textblock__body,
														[node-id="id-57-d650unu65bg"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-57-d650unu65bg"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-64361d5afc91249e3bd51e624b693b37"
																style="text-align: center">
																<span style="color: #ffffff">Precision supervision</span>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-57-d650unu65bg",
																options: []
															})
														})()
													</script>
												</div>
												<div
													class="cc-element--wrapper id-60-f88gl8t7ndg--wrapper">
													<style style-id="id-60-f88gl8t7ndg">
														@media only screen and (max-width: 767px) {
															[node-id="id-60-f88gl8t7ndg"] {
																padding-bottom: 30px;
															}
														}
														@media only screen and (min-width: 768px) {
															[node-id="id-60-f88gl8t7ndg"] {
																padding-top: 30px;
															}
														}
														[node-id="id-60-f88gl8t7ndg"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-60-f88gl8t7ndg"].cc-textblock
															.cc-textblock__body,
														[node-id="id-60-f88gl8t7ndg"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-60-f88gl8t7ndg"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #666666"
																	>Taking scenic spot resources as the basis, we will further gather surrounding available resources to create city IP. On the basis of precise monitoring, we must not only manage but also use and give full play to the multiplying potential of data elements.</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-60-f88gl8t7ndg",
																options: []
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-55-hc9k2gvgoa8"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-1 cc-col-xl-1 cc-col-lg3-1 cc-col-lg2-1 cc-col-lg-1 cc-col-md-1 cc-col-sm-1 cc-col-xs-0">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-55-hc9k2gvgoa8",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-58-9f841q1bic8--wrapper">
													<style style-id="id-58-9f841q1bic8">
														@media only screen and (min-width: 768px) {
															[node-id="id-58-9f841q1bic8"] {
																transform: translateX(-20px) translateY(25px);
															}
														}
													</style>
													<div
														node-id="id-58-9f841q1bic8"
														node-type="picture"
														class="cc-picture cc-picture--align__center">
														<img
															class="cc-picture--img"
															src="uploads/sites/1012/2022/11/198a0cc056df2660fb4e9b32ff4d2a81.png" />
													</div>

													<script>
														;(function () {
															useComponent("picture").default({
																id: "id-58-9f841q1bic8",
																options: { handler: [] }
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-56-rua8k8bgif8"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-7 cc-col-xl-7 cc-col-lg3-7 cc-col-lg2-7 cc-col-lg-7 cc-col-md-7 cc-col-sm-7 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-56-rua8k8bgif8",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-53-rmcqg93tnr8--wrapper">
													<style style-id="id-53-rmcqg93tnr8">
														[node-id="id-53-rmcqg93tnr8"] {
															background-color: rgba(0, 100, 216, 1);
															border-radius: 10px;
														}
														[node-id="id-53-rmcqg93tnr8"] .cc-textblock__body {
															padding: 30px;
														}
														[node-id="id-53-rmcqg93tnr8"].cc-textblock
															.cc-textblock__body,
														[node-id="id-53-rmcqg93tnr8"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-53-rmcqg93tnr8"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-64361d5afc91249e3bd51e624b693b37"
																style="text-align: center">
																<span style="color: #ffffff">Operational integration</span>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-53-rmcqg93tnr8",
																options: []
															})
														})()
													</script>
												</div>
												<div
													class="cc-element--wrapper id-61-89hq2o4e5v--wrapper">
													<style style-id="id-61-89hq2o4e5v">
														@media only screen and (min-width: 768px) {
															[node-id="id-61-89hq2o4e5v"] {
																padding-top: 30px;
															}
														}
														[node-id="id-61-89hq2o4e5v"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-61-89hq2o4e5v"].cc-textblock
															.cc-textblock__body,
														[node-id="id-61-89hq2o4e5v"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-61-89hq2o4e5v"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p>
																<span style="color: #666666"
																	>Replicate theme brands, management and operation models to form a business system and strategic means for the consortium.</span
																>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-61-89hq2o4e5v",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-65-9tf63av7vvo"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-65-9tf63av7vvo">
								[node-id="id-65-9tf63av7vvo"] {
									background-size: cover;
									background-position: center center;
									background-repeat: repeat;
									background-attachment: fixed;
									background-color: rgba(255, 255, 255, 1);
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-65-9tf63av7vvo",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-66-g1p0dnfgn6"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-66-g1p0dnfgn6",
											options: []
										})
									})()
								</script>
								
							</div>
						</div>
						<div
							node-id="id-79-rjce59b56t"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__row">
							<style style-id="id-79-rjce59b56t">
								[node-id="id-79-rjce59b56t"] {
									background-image: url(uploads/sites/1012/2022/11/8162fe750695744e66454981f64cd904.jpg);
									background-size: cover;
									background-position: center center;
									background-repeat: repeat;
									background-attachment: fixed;
								}
							</style>
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-79-rjce59b56t",
										options: {
											"full-width": "row",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-80-7ogc1vl6vk"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-80-7ogc1vl6vk",
											options: []
										})
									})()
								</script>
								<div class="cc-element--wrapper id-81-sr1u823ndp--wrapper">
									<div
										node-id="id-81-sr1u823ndp"
										node-type="block"
										class="cc-block cc-slot--wrapper"
										id="Program Highlights">
										<style style-id="id-81-sr1u823ndp">
											@media only screen and (max-width: 767px) {
												[node-id="id-81-sr1u823ndp"] {
													padding-top: 30px;
													padding-bottom: 30px;
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 768px) {
												[node-id="id-81-sr1u823ndp"] {
													padding-top: 60px;
													padding-bottom: 60px;
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 1360px) {
												[node-id="id-81-sr1u823ndp"] {
													padding-right: 30px;
													padding-left: 30px;
												}
											}
											@media only screen and (min-width: 1600px) {
												[node-id="id-81-sr1u823ndp"] {
													padding-right: 1px;
													padding-left: 1px;
												}
											}
										</style>
										<script>
											;(function () {
												useComponent("block").default({
													id: "id-81-sr1u823ndp",
													options: []
												})
											})()
										</script>
										<div
											node-id="id-82-pq394b6bmkg"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-82-pq394b6bmkg",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-83-fjf91a9nsko"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-83-fjf91a9nsko",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-70-e91o1k80u5--wrapper">
													<style style-id="id-70-e91o1k80u5">
														@media only screen and (min-width: 768px) {
															[node-id="id-70-e91o1k80u5"] {
																padding-bottom: 30px;
															}
														}
														[node-id="id-70-e91o1k80u5"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-70-e91o1k80u5"].cc-textblock
															.cc-textblock__body,
														[node-id="id-70-e91o1k80u5"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-70-e91o1k80u5"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p
																class="font-48058e19f5c604983923df72ff2dd684"
																style="text-align: center">
																<span style="color: #333333">Program Highlights</span>
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-70-e91o1k80u5",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-88-q6mg57hrnlg"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default custom-yy">
											<style style-id="id-88-q6mg57hrnlg">
												[node-id="id-88-q6mg57hrnlg"] {
													background-color: rgba(255, 255, 255, 1);
												}
											</style>
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-88-q6mg57hrnlg",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-89-fb5tk4tnlm"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-8 cc-col-xl-8 cc-col-lg3-8 cc-col-lg2-8 cc-col-lg-8 cc-col-md-8 cc-col-sm-8 cc-col-xs-24">
												<style style-id="id-89-fb5tk4tnlm">
													[node-id="id-89-fb5tk4tnlm"] {
														background-image: url(uploads/sites/1012/2022/11/45e2a64eccb2ef4ce950126223ccabc9.jpg);
														background-size: cover;
														background-position: center center;
														background-repeat: repeat-y;
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-89-fb5tk4tnlm",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-131-m00lda0d2ug--wrapper">
													<style style-id="id-131-m00lda0d2ug">
														@media only screen and (max-width: 767px) {
															[node-id="id-131-m00lda0d2ug"] {
																padding-top: 80px;
																padding-bottom: 80px;
															}
														}
													</style>
													<div
														node-id="id-131-m00lda0d2ug"
														node-type="placeholder"
														class="cc-placeholder"
														style="height: 50px"></div>

													<script>
														;(function () {
															useComponent("placeholder").default({
																id: "id-131-m00lda0d2ug",
																options: []
															})
														})()
													</script>
												</div>
											</div>
											<div
												node-id="id-91-lkbghsds3h"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-16 cc-col-xl-16 cc-col-lg3-16 cc-col-lg2-16 cc-col-lg-16 cc-col-md-16 cc-col-sm-16 cc-col-xs-24">
												<style style-id="id-91-lkbghsds3h">
													@media only screen and (max-width: 767px) {
														[node-id="id-91-lkbghsds3h"] {
															padding-top: 30px;
															padding-right: 30px;
															padding-bottom: 30px;
															padding-left: 30px;
														}
													}
													@media only screen and (min-width: 768px) {
														[node-id="id-91-lkbghsds3h"] {
															padding-left: 50px;
															padding-top: 50px;
															padding-bottom: 50px;
															padding-right: 50px;
														}
													}
												</style>
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-91-lkbghsds3h",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-97-6qb2m1rq298--wrapper">
													<style style-id="id-97-6qb2m1rq298">
														@media only screen and (min-width: 768px) {
															[node-id="id-97-6qb2m1rq298"] {
																padding-bottom: 30px;
															}
														}
														[node-id="id-97-6qb2m1rq298"] .cc-textblock__body {
															padding: 0px;
														}
														[node-id="id-97-6qb2m1rq298"].cc-textblock
															.cc-textblock__body,
														[node-id="id-97-6qb2m1rq298"].cc-textblock
															.cc-textblock__body
															* {
															table-layout: fixed; /*文字避首尾 -- 防止撐開*/
															word-wrap: break-word; /*英文單字因自動換行*/
															word-break: normal; /*正常避頭尾 */
															text-align: justify; /*文字左右對齊*/
															text-justify: inter-ideograph; /*用表意文本來排齊內容*/
														}
													</style>
													<div
														node-id="id-97-6qb2m1rq298"
														node-type="textblock"
														class="cc-textblock">
														<div class="cc-textblock__body richtext">
															<p class="font-64361d5afc91249e3bd51e624b693b37">
																The tourist service experience is upgraded, throughout the pre-tour, during the tour and after the tour.
															</p>
														</div>
													</div>

													<script>
														;(function () {
															useComponent("textblock").default({
																id: "id-97-6qb2m1rq298",
																options: []
															})
														})()
													</script>
												</div>
												<div
													class="cc-element--wrapper id-106-u7b4n0tfia--wrapper">
													<div
														node-id="id-106-u7b4n0tfia"
														node-type="swiper"
														class="cc-swiper cc-swiper--bullets cc-swiper--horizontal cc-swiper--progressbar__before">
														<div class="swiper-container">
															<div class="swiper-wrapper">
																<style style-id="id-106-u7b4n0tfia">
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container {
																		padding-bottom: 0px;
																	}
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container
																		> .swiper-pagination {
																		text-align: center;
																		padding: 0 0;
																	}
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container
																		> .swiper-pagination
																		> span {
																		background-color: #000;
																		opacity: 0.2;
																	}
																	[node-id="id-106-u7b4n0tfia"].cc-swiper {
																		--swiper-navigation-color: initial;
																		--swiper-pagination-color: var(
																			--swiper-theme-color
																		);
																	}
																	[node-id="id-106-u7b4n0tfia"].cc-swiper.cc-swiper--progressbar
																		.swiper-container
																		> .swiper-pagination {
																		background-color: var(--swiper-theme-color);
																	}
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container
																		> .swiper-pagination
																		> span.swiper-pagination-bullet-active {
																		background-color: var(--swiper-theme-color);
																		opacity: 1;
																	}
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container,
																	[node-id="id-106-u7b4n0tfia"]
																		> .swiper-container
																		> .swiper-wrapper
																		> .swiper-slide {
																		height: ;
																	}
																	[node-id="id-106-u7b4n0tfia"].cc-swiper--rectangle
																		.swiper-pagination-bullet-active::after {
																		-webkit-transition-duration: 0s;
																		-o-transition-duration: 0s;
																		transition-duration: 0s;
																	}
																	[node-id="id-106-u7b4n0tfia"].cc-swiper
																		> .swiper-container-vertical
																		> .swiper-button {
																		display: none;
																	}
																	[node-id="id-106-u7b4n0tfia"] .swiper-button {
																		background: ;
																		width: 27px;
																		height: 44px;
																		border-radius: 0px;
																	}
																	[node-id="id-106-u7b4n0tfia"]
																		.swiper-button-next:after,
																	[node-id="id-106-u7b4n0tfia"]
																		.swiper-button-prev:after {
																		font-size: 44px;
																	}
																</style>
																<script>
																	;(function () {
																		useComponent("swiper").default({
																			id: "id-106-u7b4n0tfia",
																			options: {
																				__note: "",
																				count: 5,
																				slidesPerView: 5,
																				"mobile-count": 1,
																				spaceBetween: 10,
																				height: "",
																				"height-full": "no",
																				direction: "horizontal",
																				loop: "no",
																				arrow: "no",
																				"arrow-location": "inner",
																				arrowColor: "",
																				"arrow-bg": "",
																				"arrow-width": "27px",
																				"arrow-height": "44px",
																				"arrow-radius": "0px",
																				"arrow-size": "44px",
																				pagination: "no",
																				paginationType: "bullets",
																				paginationLoction: "center",
																				paginationPadding: 0,
																				"pagination-bottom": "0px",
																				"progressbar-position": "before",
																				paginationColor: "",
																				PagingSubColor: "#000",
																				pagingSubOpe: "0.2",
																				effect: "slide",
																				speed: 6,
																				autoplay: 0,
																				"swiper-scale-img-enable": "no",
																				mousewheel: "no",
																				keyboard: "no",
																				"init-animation": [],
																				"hover-animation": [],
																				marquee_suspend: "yes"
																			}
																		})
																	})()
																</script>
																<div
																	class="swiper-slide cc-slot--wrapper cc-slot--id-106-u7b4n0tfia">
																	<div
																		node-id="id-107-gc63g09771o"
																		node-type="row"
																		class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																		<script>
																			;(function () {
																				useComponent("row").default({
																					id: "id-107-gc63g09771o",
																					options: {
																						"full-width": "default",
																						"adaption-height": "no",
																						"background-video": "",
																						"noheader-full-height": "no",
																						"auto-flex": [],
																						"auto-flex-enable": "no"
																					}
																				})
																			})()
																		</script>
																		<div
																			node-id="id-108-oikemr758d"
																			node-type="column"
																			class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																			<script>
																				;(function () {
																					useComponent("column").default({
																						id: "id-108-oikemr758d",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				class="cc-element--wrapper id-135-5fe3c1dej58--wrapper">
																				<style style-id="id-135-5fe3c1dej58">
																					[node-id="id-135-5fe3c1dej58"] {
																						background-color: rgba(
																							250,
																							250,
																							250,
																							1
																						);
																						border-radius: 5px;
																					}
																					[node-id="id-135-5fe3c1dej58"]
																						.cc-imagetext--image {
																						width: 36%;
																					}
																					[node-id="id-135-5fe3c1dej58"]
																						.cc-imagetext--text {
																						width: calc(100% - 36%);
																					}
																					[node-id="id-135-5fe3c1dej58"]
																						.cc-imagetext--body
																						.cc-imagetext--image {
																						text-align: center;
																					}
																				</style>
																				<div
																					node-id="id-135-5fe3c1dej58"
																					node-type="imagetext"
																					class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top">
																					<div class="cc-imagetext--body">
																						<div class="cc-imagetext--image">
																							<a target="_self"
																								><img
																									src="uploads/sites/1012/2022/12/7a3cf46ca005a14b56ce6e2cad96b10a.png"
																							/></a>
																						</div>

																						<div
																							class="cc-imagetext--text richtext">
																							<p style="text-align: center">
																								No feeling of admission
																							</p>
																						</div>
																					</div>
																				</div>

																				<script>
																					;(function () {
																						useComponent("imagetext").default({
																							id: "id-135-5fe3c1dej58",
																							options: { href_shared: "no" }
																						})
																					})()
																				</script>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	class="swiper-slide cc-slot--wrapper cc-slot--id-106-u7b4n0tfia">
																	<div
																		node-id="id-110-nnr8f8ekadg"
																		node-type="row"
																		class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																		<script>
																			;(function () {
																				useComponent("row").default({
																					id: "id-110-nnr8f8ekadg",
																					options: {
																						"full-width": "default",
																						"adaption-height": "no",
																						"background-video": "",
																						"noheader-full-height": "no",
																						"auto-flex": [],
																						"auto-flex-enable": "no"
																					}
																				})
																			})()
																		</script>
																		<div
																			node-id="id-111-ba85h7d59n8"
																			node-type="column"
																			class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																			<script>
																				;(function () {
																					useComponent("column").default({
																						id: "id-111-ba85h7d59n8",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				class="cc-element--wrapper id-130-vntb19rt5cg--wrapper">
																				<style style-id="id-130-vntb19rt5cg">
																					[node-id="id-130-vntb19rt5cg"] {
																						background-color: rgba(
																							250,
																							250,
																							250,
																							1
																						);
																						border-radius: 5px;
																					}
																					[node-id="id-130-vntb19rt5cg"]
																						.cc-imagetext--image {
																						width: 36%;
																					}
																					[node-id="id-130-vntb19rt5cg"]
																						.cc-imagetext--text {
																						width: calc(100% - 36%);
																					}
																					[node-id="id-130-vntb19rt5cg"]
																						.cc-imagetext--body
																						.cc-imagetext--image {
																						text-align: center;
																					}
																				</style>
																				<div
																					node-id="id-130-vntb19rt5cg"
																					node-type="imagetext"
																					class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top">
																					<div class="cc-imagetext--body">
																						<div class="cc-imagetext--image">
																							<a target="_self"
																								><img
																									src="uploads/sites/1012/2022/12/883a1175efbb8bd9397c2743fb71118a.png"
																							/></a>
																						</div>

																						<div
																							class="cc-imagetext--text richtext">
																							<p style="text-align: center">
																								Smart Navigation
																							</p>
																						</div>
																					</div>
																				</div>

																				<script>
																					;(function () {
																						useComponent("imagetext").default({
																							id: "id-130-vntb19rt5cg",
																							options: { href_shared: "no" }
																						})
																					})()
																				</script>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	class="swiper-slide cc-slot--wrapper cc-slot--id-106-u7b4n0tfia">
																	<div
																		node-id="id-113-obhbcjtm4mg"
																		node-type="row"
																		class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																		<script>
																			;(function () {
																				useComponent("row").default({
																					id: "id-113-obhbcjtm4mg",
																					options: {
																						"full-width": "default",
																						"adaption-height": "no",
																						"background-video": "",
																						"noheader-full-height": "no",
																						"auto-flex": [],
																						"auto-flex-enable": "no"
																					}
																				})
																			})()
																		</script>
																		<div
																			node-id="id-114-689bl7ljf7g"
																			node-type="column"
																			class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																			<script>
																				;(function () {
																					useComponent("column").default({
																						id: "id-114-689bl7ljf7g",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				class="cc-element--wrapper id-132-e0d9lepupsg--wrapper">
																				<style style-id="id-132-e0d9lepupsg">
																					[node-id="id-132-e0d9lepupsg"] {
																						background-color: rgba(
																							250,
																							250,
																							250,
																							1
																						);
																						border-radius: 5px;
																					}
																					[node-id="id-132-e0d9lepupsg"]
																						.cc-imagetext--image {
																						width: 36%;
																					}
																					[node-id="id-132-e0d9lepupsg"]
																						.cc-imagetext--text {
																						width: calc(100% - 36%);
																					}
																					[node-id="id-132-e0d9lepupsg"]
																						.cc-imagetext--body
																						.cc-imagetext--image {
																						text-align: center;
																					}
																				</style>
																				<div
																					node-id="id-132-e0d9lepupsg"
																					node-type="imagetext"
																					class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top">
																					<div class="cc-imagetext--body">
																						<div class="cc-imagetext--image">
																							<a target="_self"
																								><img
																									src="uploads/sites/1012/2022/12/fb60737a475c4978915ac88bc7bd2444.png"
																							/></a>
																						</div>

																						<div
																							class="cc-imagetext--text richtext">
																							<p style="text-align: center">
																								Travel short video
																							</p>
																						</div>
																					</div>
																				</div>

																				<script>
																					;(function () {
																						useComponent("imagetext").default({
																							id: "id-132-e0d9lepupsg",
																							options: { href_shared: "no" }
																						})
																					})()
																				</script>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	class="swiper-slide cc-slot--wrapper cc-slot--id-106-u7b4n0tfia">
																	<div
																		node-id="id-121-k5ahrjeb4n"
																		node-type="row"
																		class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																		<script>
																			;(function () {
																				useComponent("row").default({
																					id: "id-121-k5ahrjeb4n",
																					options: {
																						"full-width": "default",
																						"adaption-height": "no",
																						"background-video": "",
																						"noheader-full-height": "no",
																						"auto-flex": [],
																						"auto-flex-enable": "no"
																					}
																				})
																			})()
																		</script>
																		<div
																			node-id="id-122-av5u9mmo1mg"
																			node-type="column"
																			class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																			<script>
																				;(function () {
																					useComponent("column").default({
																						id: "id-122-av5u9mmo1mg",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				class="cc-element--wrapper id-133-3kgb54q1nq8--wrapper">
																				<style style-id="id-133-3kgb54q1nq8">
																					[node-id="id-133-3kgb54q1nq8"] {
																						background-color: rgba(
																							250,
																							250,
																							250,
																							1
																						);
																						border-radius: 5px;
																					}
																					[node-id="id-133-3kgb54q1nq8"]
																						.cc-imagetext--image {
																						width: 36%;
																					}
																					[node-id="id-133-3kgb54q1nq8"]
																						.cc-imagetext--text {
																						width: calc(100% - 36%);
																					}
																					[node-id="id-133-3kgb54q1nq8"]
																						.cc-imagetext--body
																						.cc-imagetext--image {
																						text-align: center;
																					}
																				</style>
																				<div
																					node-id="id-133-3kgb54q1nq8"
																					node-type="imagetext"
																					class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top">
																					<div class="cc-imagetext--body">
																						<div class="cc-imagetext--image">
																							<a target="_self"
																								><img
																									src="uploads/sites/1012/2022/12/c737048491c2317112e2ca8cc1ea060c.png"
																							/></a>
																						</div>

																						<div
																							class="cc-imagetext--text richtext">
																							<p style="text-align: center">
																								Traffic News
																							</p>
																						</div>
																					</div>
																				</div>

																				<script>
																					;(function () {
																						useComponent("imagetext").default({
																							id: "id-133-3kgb54q1nq8",
																							options: { href_shared: "no" }
																						})
																					})()
																				</script>
																			</div>
																		</div>
																	</div>
																</div>
																<div
																	class="swiper-slide cc-slot--wrapper cc-slot--id-106-u7b4n0tfia">
																	<div
																		node-id="id-126-qqhmladlvng"
																		node-type="row"
																		class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
																		<script>
																			;(function () {
																				useComponent("row").default({
																					id: "id-126-qqhmladlvng",
																					options: {
																						"full-width": "default",
																						"adaption-height": "no",
																						"background-video": "",
																						"noheader-full-height": "no",
																						"auto-flex": [],
																						"auto-flex-enable": "no"
																					}
																				})
																			})()
																		</script>
																		<div
																			node-id="id-127-0k49ck4o388"
																			node-type="column"
																			class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
																			<script>
																				;(function () {
																					useComponent("column").default({
																						id: "id-127-0k49ck4o388",
																						options: []
																					})
																				})()
																			</script>
																			<div
																				class="cc-element--wrapper id-134-6gltpch3gg--wrapper">
																				<style style-id="id-134-6gltpch3gg">
																					[node-id="id-134-6gltpch3gg"] {
																						background-color: rgba(
																							250,
																							250,
																							250,
																							1
																						);
																						border-radius: 5px;
																					}
																					[node-id="id-134-6gltpch3gg"]
																						.cc-imagetext--image {
																						width: 36%;
																					}
																					[node-id="id-134-6gltpch3gg"]
																						.cc-imagetext--text {
																						width: calc(100% - 36%);
																					}
																					[node-id="id-134-6gltpch3gg"]
																						.cc-imagetext--body
																						.cc-imagetext--image {
																						text-align: center;
																					}
																				</style>
																				<div
																					node-id="id-134-6gltpch3gg"
																					node-type="imagetext"
																					class="cc-imagetext cc-imagetext--layout__vertical cc-imagetext--top">
																					<div class="cc-imagetext--body">
																						<div class="cc-imagetext--image">
																							<a target="_self"
																								><img
																									src="uploads/sites/1012/2022/12/822ecf475fc090e5f6f35cd3224de512.png"
																							/></a>
																						</div>

																						<div
																							class="cc-imagetext--text richtext">
																							<p style="text-align: center">
																								Smart Payment
																							</p>
																						</div>
																					</div>
																				</div>

																				<script>
																					;(function () {
																						useComponent("imagetext").default({
																							id: "id-134-6gltpch3gg",
																							options: { href_shared: "no" }
																						})
																					})()
																				</script>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div
											node-id="id-12-34for87q2co"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-12-34for87q2co",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-13-u69d1kqo90g"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-13-u69d1kqo90g",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-14-hv8ppjlpde--wrapper">
													<div
														node-id="id-14-hv8ppjlpde"
														node-type="placeholder"
														class="cc-placeholder"
														style="height: 30px"></div>

													<script>
														;(function () {
															useComponent("placeholder").default({
																id: "id-14-hv8ppjlpde",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-136-bf7np23h6e8"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default custom-yy">
											<style style-id="id-136-bf7np23h6e8">
												[node-id="id-136-bf7np23h6e8"] {
													background-color: rgba(255, 255, 255, 1);
												}
											</style>
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-136-bf7np23h6e8",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											
										</div>
										<div
											node-id="id-17-4hkpuhbv35"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-17-4hkpuhbv35",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											<div
												node-id="id-18-s90h70oi8m8"
												node-type="column"
												class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
												<script>
													;(function () {
														useComponent("column").default({
															id: "id-18-s90h70oi8m8",
															options: []
														})
													})()
												</script>
												<div
													class="cc-element--wrapper id-19-dvotiktidlo--wrapper">
													<div
														node-id="id-19-dvotiktidlo"
														node-type="placeholder"
														class="cc-placeholder"
														style="height: 30px"></div>

													<script>
														;(function () {
															useComponent("placeholder").default({
																id: "id-19-dvotiktidlo",
																options: []
															})
														})()
													</script>
												</div>
											</div>
										</div>
										<div
											node-id="id-20-dt2dceakg2o"
											node-type="row"
											class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default custom-yy">
											<style style-id="id-20-dt2dceakg2o">
												[node-id="id-20-dt2dceakg2o"] {
													background-color: rgba(255, 255, 255, 1);
												}
											</style>
											<script>
												;(function () {
													useComponent("row").default({
														id: "id-20-dt2dceakg2o",
														options: {
															"full-width": "default",
															"adaption-height": "no",
															"background-video": "",
															"noheader-full-height": "no",
															"auto-flex": [],
															"auto-flex-enable": "no"
														}
													})
												})()
											</script>
											
											
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							node-id="id-100-mskhfsu820g"
							node-type="row"
							class="cc-row cc-slot--wrapper cc-row--flex cc-row--justify__start cc-row--align__top cc-row--width__default">
							<script>
								;(function () {
									useComponent("row").default({
										id: "id-100-mskhfsu820g",
										options: {
											"full-width": "default",
											"adaption-height": "no",
											"background-video": "",
											"noheader-full-height": "no",
											"auto-flex": [],
											"auto-flex-enable": "no"
										}
									})
								})()
							</script>
							<div
								node-id="id-101-gbs13s0daf"
								node-type="column"
								class="cc-col cc-slot--wrapper cc-col--align__top cc-col--justify__start cc-col-24 cc-col-xl-24 cc-col-lg3-24 cc-col-lg2-24 cc-col-lg-24 cc-col-md-24 cc-col-sm-24 cc-col-xs-24">
								<script>
									;(function () {
										useComponent("column").default({
											id: "id-101-gbs13s0daf",
											options: []
										})
									})()
								</script>
								
							</div>
						</div>
					</div>
					<div class="Page-sidebar sidebar-right"></div>
				</div>

				<div
					class="Page-footer"
					style="background: #020e26; display: flex; justify-content: center">
					<div class="cc-textblock__body richtext">
						<div style="color: white; font-size: 20px">Contact Details</div>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Company: Digital Origin (Hong Kong) Limited</span
							>
						</p>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Address: Room 608-613, 6/F, Block C, Cyberport 3, 100 Cyberport Road, Hong Kong</span
							>
						</p>
						<p style="line-height: 1.6">
							<span style="color: #7b8595; font-size: 14px"
								>Tel: 00852-56147493</span
							>
						</p>
						<!-- <p style="line-height: 1.6;"><span style="color: #7b8595; font-size: 14px;">投訴：0755-88328980</span></p> -->
						<p style="line-height: 1.6; text-align: left">
							<span style="color: #7b8595; font-size: 14px"
								>Email: <EMAIL>; <EMAIL>.</span
							>
						</p>
					</div>
					<div class="cc-textblock__body richtext">
						<div style="color: white; font-size: 20px">Follow us</div>
						<div
							node-id="id-40-k3cqy7zenf"
							node-type="imagelist"
							class="cc-imagelist cc-imagelist--grid">
							<div class="cc-imagelist-wrapper">
								<div class="cc-imagelist--items">
									<div class="cc-imagelist--item">
										<div class="cc-imagelist--item__link">
											<a href="" title="" target="_self">
												<div class="cc-imagelist--item__image">
													<img
														src="uploads/sites/1012/2022/11/14df9f2ffdcd56437226404d4d0993a1.jpg" />
												</div>
												<div class="cc-imagelist--item__title"></div>
												<div class="cc-imagelist--item__mobile-title"></div>
											</a>
										</div>
									</div>
									<div class="cc-imagelist--item">
										<div class="cc-imagelist--item__link">
											<a href="" title="" target="_self">
												<div class="cc-imagelist--item__image">
													<img
														src="uploads/sites/1012/2022/11/d9817bde75e239270021cd0e76b9e947.jpg" />
												</div>
												<div class="cc-imagelist--item__title"></div>
												<div class="cc-imagelist--item__mobile-title"></div>
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="Page-widgets">
					<div class="side-toolbar position-right-bottom">
						<ul>
							<li>
								<!-- <div class="toolbar-icon
                ">
                <a target="_self" title="在線客服" onclick="javascript:_utils_.handler(JSON.parse(decodeURIComponent('%7B%22action%22%3A%22open%22%2C%22options%22%3A%7B%22qq%22%3A%22919618856%22%2C%22target%22%3A%22_self%22%2C%22url%22%3A%22https%3A%5C%2F%5C%2Fgcluster.shukeyun.com%5C%2Fmeta%5C%2Fchat%5C%2F%22%7D%7D')));" href="javascript:;"><img src="uploads/sites/1012/2024/07/482b135a21e71b06ae1227aaf7083817.png"></a>            </div> -->
							</li>
							<li>
								<div class="toolbar-icon">
									<a
										target="_self"
										title="返回頂部"
										onclick="javascript:_utils_.handler(JSON.parse(decodeURIComponent('%7B%22action%22%3A%22backtop%22%2C%22options%22%3A%5B%5D%7D')));"
										href="javascript:;"
										><img
											src="uploads/sites/1012/2024/09/11e5641042b1b3cfe9a1a3ec4073aff5.png"
									/></a>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<script class="custom-js-code">
			;(function () {
				var custom_js_code = [{ title: "js\u793a\u4f8b", code: "" }]
				for (var i = 0; i < custom_js_code.length; i++) {
					var code = custom_js_code[i].code
					var title = custom_js_code[i].title
					_utils_.sandbox(code, title)
				}
			})()
		</script>
	</body>
</html>

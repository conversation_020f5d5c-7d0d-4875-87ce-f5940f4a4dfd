/* theme style */


:root {
    --black: #000;
    --white: #fff;
    --text-color: #333333;
    --border-color: #EEEEEE;
    --active-color: #3095fb;
    --background-color: #FFFFFF;
    --background-color-light: #10014fe;

    --theme-color: #0064D8;
    --theme-color-dark: #0064D8;
    --theme-color-light: #0064D8;

    --warning-color: #ffa179;
    --warning-color-dark: #ff976a;
    --warning-color-light: #fffbe8;

    --success-color: #20c770;
    --success-color-dark: #07c160;
    --success-color-light: #b5eccf;

    --error-color: #ff5757;
    --error-color-dark: #f44;
    --error-color-light: #ffc7c7;

    --font-size: 16px;
    --line-height: 1.4;
    --center-width: 1400px;
}

::placeholder {
    color: #BBBBBB;
}

a {
    color: #333333;
}

a:hover {
    color: #0064D8;
}

body {
    color: #333333;
    font-family: "微软雅黑",<PERSON><PERSON>,"Microsoft YaHei",sans-serif,"Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB";;
    font-size: 16px;
    line-height: 1.4;
    background-attachment:scroll;}

.Page {
    background-attachment:scroll;}


/* 版心是否包含侧边栏 */
.both-sidebar .Page-body,
.left-sidebar .Page-body,
.right-sidebar .Page-body {
    width: 1400px;
}
    
.Page-header--topbar {
    background-attachment:scroll;}

.Page-header {
    background-attachment:scroll;}

.Page-header .Page-header--topbar .top-bar__in .top-bar__body .account a{
    color:#333333;
}
.Page-header .Page-header--topbar .top-bar__in .top-bar__body .account a:hover{
    color:#0064D8;
}


.Page-slot--template-header_in{
    width: 250px;
}


.Page-header--main.default,
.Page-header--mobile.default {
    }

.Page .Page-header .Page-header--main.default {
    border-bottom: 1px solid rgba(0,0,0,0);
}

.Page-header--main,
.Page-header--mobile {
    background-attachment:scroll;background-color:rgba(27, 28, 55, 1);}

.Page-header--main:hover {
    background-color: ;
}

.Page-header--main.fixed,
.Page-header--mobile.fixed {
    }

.Page-header--main.fixed {
    background-attachment:scroll;background-color:rgba(27, 28, 55, 1);}

.Page-header--mobile,
.show-mobile-menu .Page-header::before {
    background-attachment:scroll;background-color:rgba(27, 28, 55, 1);}
.Page-header--mobile .Page-header--icons li.menu {
    color: #333333;
}
.Page .Page-header .Page-header--main .Page-header--icons li.menu{
    color: #333333;
}

.Page .Page-header .Page-header--main .Page-header--icons li.menu.icon-active,
.Page-header--mobile .Page-header--icons li.menu.icon-active {
    color: #0064D8;
}

.Page .Page-header .Page-header--main .Page-header--icons li.menu-colse{
    color: #000;
}

.Page-header--subheader {
    height: 250px;
    background-attachment:scroll;}
.Page .Page-header  .Page-header--icons .menu i{
    font-size: initial ;
}

.Page-header--main .cc-menu--nav .cc-menu--item .cc-menu--item__link{justify-content:flex-start;text-align:left;} .Page-header--mobile .cc-menu--nav .cc-menu--item .cc-menu--item__link{justify-content:flex-start;text-align:left;} .Page-header--shade .cc-menu--nav .cc-menu--item .cc-menu--item__link{justify-content:flex-start;text-align:left;} 

.Page-header--user > a{
    color:#333333;
}

.Page-content {
    background-attachment:scroll;}

.Page-sidebar {
    background-attachment:scroll;}

.Page-footer>.footer {
    background-attachment:scroll;}

/** 常规版心 */
.container, .cc-row--width__default {
    max-width: 1400px;
}

.cc-row--width__row {
    padding-left: calc((100% - 1400px) / 2) !important;
    padding-right: calc((100% - 1400px) / 2) !important;
}


/** 头部版心 */
.Page-header .container, .Page-header .cc-row--width__default {
    max-width: 1400px;
}


.Page-header .cc-row--width__row {
    padding-left: calc((100% - 1400px) / 2) !important;
    padding-right: calc((100% - 1400px) / 2) !important;
}


.Page-header--main__in {
    padding: 20px 0;
}


h1 {
    font-size: 32px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}

h2 {
    font-size: 24px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}

h3 {
    font-size: 18px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}

h4 {
    font-size: 16px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}

h5 {
    font-size: 13px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}

h6 {
    font-size: 12px;
    line-height: 1.4;
    font-weight:bold;
    color: #3c3c3c;
}



.theme-color-text {
    color: #0064D8;
}

.theme-color-bg {
    background-color: #0064D8;
}






/* 框式布局 */
.layout-boxed .Page {
    max-width: 1400px;
}


/* 文章详情宽度 */
.Page .posts {
    max-width: 780px;
}
/* 文章相册主图与缩略图间距 */
.Page .posts .posts-body .posts-photos .cc-gallery .cc-gallery--thumbs {
    padding: 10px 0;
}




                .side-toolbar.position-right-bottom{
                    bottom: 10%;
                    right: 0;
                    height: max-content;
                }
            
        .Page .Page-widgets .side-toolbar .toolbar-text.show{
            transform: translateX(calc(-100% + 3px));
            -webkit-transform: translateX(calc(-100% + 3px));
            -ms-transform: translateX(calc(-100% + 3px));
        }
        .Page .Page-widgets .side-toolbar .toolbar-img.show{
            right: calc( 100% + 3px );
            display: block;
        }
    
/* 侧边栏宽度 */
body.both-sidebar .Page-sidebar.sidebar-left,
body.left-sidebar .Page-sidebar.sidebar-left
 {
    width: 23%;
}
body.both-sidebar .Page-sidebar.sidebar-right,
body.right-sidebar .Page-sidebar.sidebar-right
 {
    width: 23%;
}
/* 内容宽度 */
body.both-sidebar .Page-content {
    width: 54%;
}
body.left-sidebar .Page-content {
    width: 77%;
}
body.right-sidebar .Page-content {
    width: 77%;
}

/* 底部 */
.Page-footer>.footer {
    padding: 30px 0;
}

/* 底部工具条 */
.Page-footer > .fixed-toolbar{
    padding:0px 0;
}

/* 悬浮广告窗 */
.Page-widgets > .fixed-toolbar.page-move-fixed{
    width:200px;
}



.side-toolbar .toolbar-icon {
    background-color: rgba(246, 246, 246, 0);
    width: 200px;
    height: 200px;
}
.side-toolbar > ul > li:hover .toolbar-icon,
.side-toolbar .toolbar-text {
    background-color: rgba(255, 0, 0, 0);
}
.side-toolbar > ul > li {
    margin-bottom: 5px;
}


/** 头部组件排序 */
.Page-header--main .Page-header--logo {
    order: 0;
}

.Page-header--main .Page-header--menu {
    order: 1;
}
.Page-header--main .Page-header--widgets {
    order: 2;
}
.Page-header--main .Page-slot--template-header_in {
    order: 3;
    min-width: 250px}


.Page .Page-body .Page-content .join{
    background-color:#fff;
}


.Page-header--logo img {
    max-height: 60px;
    width: auto;
}

    .header-type-fixed .Page-header .Page-header--main.fixed .Page-header--logo img.all-logo,
    .header-type-immersion .Page-header .Page-header--main .Page-header--logo img.all-logo,
    .header-type-immersion2 .Page-header .Page-header--main .Page-header--logo img.all-logo {
        max-height: 60px;
        width: auto;
    }
    






/** 覆盖菜单菜单关闭按钮颜色设置 */
.menu-colse{
    color: #000}





/** 产品按钮 */
.product .checkout-goto {
    background-color: var(--theme-color);
}
.product .checkout-goto:hover {
    background-color: var(--theme-color-dark);
}
.product .shopping-push {
    background-color: var(--error-color);
}
.product .shopping-push:hover {
    background-color: var(--error-color-dark);
}

/** 询价按钮 */
/** 询价按钮悬停样式 */


/* 产品详情 */
.Page .Page-body .Page-content .product > .cc-row{
    padding: 0 0px;
}
.Page .product .cc-gallery .cc-gallery--top{
    width:100%;
}
.Page .product .cc-gallery .cc-gallery--top .swiper-zoom-container{
    justify-content:center;
}
.Page .product .cc-row--width__default .cc-element--wrapper .cc-textblock .cc-textblock__body{
    padding:20px}
.Page .product .cc-row--width__default .product-header{
    margin-bottom:0px}
.product .cc-tabs.cc-tabs--line.cc-tabs--position__top .cc-tabs--nav:after{
    bottom: 0;    
}


.product .product-header .cc-product--attrs .product-info .product-property{
    margin: 0px 0;    
}





    .product-header{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
    }
    .product .cc-gallery--top{
        max-height: 380px
    }






        .user-page .field-card{
            background-color:#fff;
        }
        .account .user-avatar::before{
            border: 3px solid #fff;
        }
        .user-page.order .order-item--head{
            background-color: rgba(214, 214, 214, 1);
        }
        .user-page .cc-account-sidenav{
            background-color:#fff;
        }
        .user-page .cc-account-sidenav--menu dd{
            background-color:#fff;
        }
        .user-page .cc-account-sidenav--menu dd a{
            color:rgba(96, 96, 96, 1);
        }
        .user-page .cc-account-sidenav--menu dd i{
            color: rgba(96, 96, 96, 1);
        }
        .user-page .cc-account-sidenav--menu dd:hover{
            background-color:rgba(214, 214, 214, 1);
        }
    
        .user-page .cc-account-sidenav--menu dd:hover i,
        .user-page .cc-account-sidenav--menu dd:hover a{
            color:rgba(0, 0, 0, 1);
        }
        
        .user-page .cc-account-sidenav--menu .current{
            background-color:rgba(214, 214, 214, 1);
        }
        .user-page .cc-account-sidenav--menu .current i,
        .user-page .cc-account-sidenav--menu .current a{
            color:rgba(0, 0, 0, 1);
        }
    









/* 小于版心时 */
@media only screen and (max-width: 1400px) {
    .container, .cc-row--width__default {
        max-width: 98%;
    }
    .container .container, .cc-row .cc-row--width__default {
        max-width: 100%;
    }
    .layout-boxed .Page {
        max-width: calc(100% - 60px);
    }
}

/* 自定义分辨率的导航栏 */
@media only screen and (max-width: 769px) {
    
    
        .show-mobile-menu .Page-header::before{
        background-color: transparent !important;
    }
    
    }

@media only screen and (min-width: 770px ){
    .Page-header--mobile {
        display: none;
    }

    }
@media only screen and (max-width: 770px ){
    .Page-header--mobile .Page-header--left-drawer,
    .Page-header--mobile .Page-header--right-drawer{
        flex-direction: row-reverse;
    }
}


@media only screen and (max-width:769px){
    }


        @media only screen and (max-width: 769px) {
            .Page-header .Page-header--mobile .Page-header--simple,
            .Page-header .Page-header--mobile .Page-header--left-drawer,
            .Page-header .Page-header--mobile .Page-header--right-drawer {
                border-bottom: 1px solid rgba(0,0,0,0);
            }
        }
    

        @media only screen and (max-width: 769px) {
            .cc-textblock table,
            .posts .richtext table,
            .product .richtext table {
                margin: 5px 0 20px 0;
                display: block;
                width: 100%;
                overflow-x: auto;
                overflow-y: auto;
                min-height:100px;
            }
            .cc-textblock table tbody,
            .posts .richtext table tbody,
            .product .richtext table tbody {
                display: inline-table;
                width: 100%;
                height: inherit;
                border-width:inherit;
            }
            .cc-textblock table td,
            .posts .richtext table td,
            .product .richtext table td {
                white-space:nowrap
            }
        }
    

        @media only screen and (max-width: 769px) {
            .Page-header .Page-header--mobile{
                background-color: #fff;
            }
        }
    





.font-48058e19f5c604983923df72ff2dd684{font-size:36px !important;} @media only screen and (max-width: 767px) {.font-48058e19f5c604983923df72ff2dd684{font-size:30px !important;} }@media only screen and (min-width: 768px) {.font-48058e19f5c604983923df72ff2dd684{font-size:36px !important;} }.font-64361d5afc91249e3bd51e624b693b37{font-size:24px !important;} @media only screen and (max-width: 767px) {.font-64361d5afc91249e3bd51e624b693b37{font-size:20px !important;} }@media only screen and (min-width: 768px) {.font-64361d5afc91249e3bd51e624b693b37{font-size:24px !important;} }.font-1ea24a2ccc91fca49f6e4455d2a5f757{font-size:48px !important;} @media only screen and (max-width: 767px) {.font-1ea24a2ccc91fca49f6e4455d2a5f757{font-size:36px !important;} }@media only screen and (min-width: 768px) {.font-1ea24a2ccc91fca49f6e4455d2a5f757{font-size:48px !important;} }.font-90faa2088d198f670edbb6dc65766877{font-size:30px !important;} @media only screen and (max-width: 767px) {.font-90faa2088d198f670edbb6dc65766877{font-size:24px !important;} }@media only screen and (min-width: 768px) {.font-90faa2088d198f670edbb6dc65766877{font-size:30px !important;} }
